{"dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@reduxjs/toolkit": "^2.7.0", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "next": "^15.3.1", "next-auth": "^4.24.11", "next-connect": "^1.0.0", "nodemailer": "^6.10.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-react": "^7.22.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "autoprefixer": "^10.4.14", "babel-jest": "^29.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-watch-typeahead": "^2.2.2", "node-fetch": "^3.3.1", "postcss": "^8.4.23", "tailwindcss": "^3.3.0", "typescript": "^5.1.6"}, "name": "homie<PERSON>", "version": "1.0.0", "private": true, "description": "Homieye is a web application that helps users find rooms, apartments, and compatible roommates worldwide.", "author": "<PERSON><PERSON><PERSON>", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}}