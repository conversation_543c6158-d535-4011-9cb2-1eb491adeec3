// utils/dummyData.js

export const dummyListings = [
    {
      id: '1',
      title: 'Modern Shared Apartment in Kadıköy',
      type: 'Shared Apartment',
      location: 'Kadıköy, Istanbul',
      price: 3200,
      currency: 'TRY',
      period: 'month',
      beds: 3,
      baths: 1,
      area: 110,
      image: 'https://source.unsplash.com/random/800x600/?apartment,modern',
      verified: true,
      featured: true,
      available: 'Immediately',
      furnished: true,
      gender: 'mixed',
      description: 'Bright and spacious apartment in the heart of Kadıköy. Three bedrooms, fully furnished with a modern kitchen, high-speed internet, and a cozy living room. Walking distance to public transportation, cafes, and shops.',
      amenities: ['Wifi', 'Air Conditioning', 'Washing Machine', 'Dishwasher', 'Balcony', 'Elevator'],
      roommates: 2,
      lastActive: '2 hours ago'
    },
    {
      id: '2',
      title: 'Cozy Private Room in Beşiktaş',
      type: 'Private Room',
      location: 'Beşiktaş, Istanbul',
      price: 1800,
      currency: 'TRY',
      period: 'month',
      beds: 1,
      baths: 1,
      area: 25,
      image: 'https://source.unsplash.com/random/800x600/?bedroom,cozy',
      verified: true,
      featured: true,
      available: 'May 1, 2025',
      furnished: true,
      gender: 'female',
      description: 'Private room in a 3-bedroom apartment, perfect for female students or professionals. The room includes a comfortable bed, desk, wardrobe, and has lots of natural light. Shared kitchen and living area with two friendly roommates.',
      amenities: ['Wifi', 'Shared Kitchen', 'Washing Machine', 'Study Desk', 'Central Location'],
      roommates: 2,
      lastActive: '1 day ago'
    },
    {
      id: '3',
      title: 'Stylish Apartment with Bosphorus View',
      type: 'Short Stay',
      location: 'Üsküdar, Istanbul',
      price: 250,
      currency: 'TRY',
      period: 'night',
      beds: 2,
      baths: 1,
      area: 80,
      image: 'https://source.unsplash.com/random/800x600/?apartment,view',
      verified: true,
      featured: true,
      available: 'Immediately',
      furnished: true,
      gender: 'any',
      description: 'Gorgeous apartment with stunning Bosphorus views, perfect for short stays or vacation rentals. Fully equipped kitchen, high-speed WiFi, and all amenities for a comfortable stay. Minimum booking: 3 nights.',
      amenities: ['Bosphorus View', 'Air Conditioning', 'Full Kitchen', 'TV', '24/7 Check-in', 'Towels & Linens'],
      roommates: 0,
      lastActive: '3 hours ago'
    },
    {
      id: '4',
      title: 'Student-Friendly Apartment near Universities',
      type: 'Shared Apartment',
      location: 'Şişli, Istanbul',
      price: 2500,
      currency: 'TRY',
      period: 'month',
      beds: 2,
      baths: 1,
      area: 70,
      image: 'https://source.unsplash.com/random/800x600/?student,apartment',
      verified: false,
      featured: true,
      available: 'June 15, 2025',
      furnished: false,
      gender: 'male',
      description: 'Two-bedroom apartment ideal for students, located close to major universities. Unfurnished but with essential appliances included. Great neighborhood with plenty of cafes, markets, and easy access to public transportation.',
      amenities: ['Near University', 'Fridge', 'Stove', 'Public Transport Nearby'],
      roommates: 1,
      lastActive: '5 days ago'
    },
    {
      id: '5',
      title: 'Luxury Penthouse for Short Stays',
      type: 'Short Stay',
      location: 'Beyoğlu, Istanbul',
      price: 450,
      currency: 'TRY',
      period: 'night',
      beds: 3,
      baths: 2,
      area: 150,
      image: 'https://source.unsplash.com/random/800x600/?penthouse,luxury',
      verified: true,
      featured: true,
      available: 'Immediately',
      furnished: true,
      gender: 'any',
      description: 'Spectacular penthouse in the heart of Beyoğlu with breathtaking city views. Luxury furnishings, spacious terrace, and premium amenities. Perfect for a special occasion or for those who appreciate the finer things in life.',
      amenities: ['Terrace', 'Air Conditioning', 'Smart TV', 'Premium Furniture', 'City View', 'Security'],
      roommates: 0,
      lastActive: '1 hour ago'
    },
    {
      id: '6',
      title: 'Comfortable Room in Historic District',
      type: 'Private Room',
      location: 'Fatih, Istanbul',
      price: 1500,
      currency: 'TRY',
      period: 'month',
      beds: 1,
      baths: 1,
      area: 20,
      image: 'https://source.unsplash.com/random/800x600/?historic,room',
      verified: true,
      featured: true,
      available: 'May 10, 2025',
      furnished: true,
      gender: 'male',
      description: 'Private room in a traditional Turkish house in the historic Fatih district. The room is fully furnished with a single bed, wardrobe, and desk. Great opportunity to experience authentic Istanbul living while being close to major historical sites.',
      amenities: ['Wifi', 'Heating', 'Washing Machine', 'Historic Building', 'Tourist Attractions Nearby'],
      roommates: 3,
      lastActive: '2 days ago'
    },
    {
      id: '7',
      title: 'Bright Studio Apartment in Levent',
      type: 'Private Room',
      location: 'Levent, Istanbul',
      price: 3500,
      currency: 'TRY',
      period: 'month',
      beds: 1,
      baths: 1,
      area: 40,
      image: 'https://source.unsplash.com/random/800x600/?studio,bright',
      verified: true,
      featured: false,
      available: 'Immediately',
      furnished: true,
      gender: 'any',
      description: 'Modern studio apartment in the business district of Levent. Fully furnished with contemporary decor, perfect for young professionals. Close to metro station, shopping malls, and business centers.',
      amenities: ['Wifi', 'Air Conditioning', 'Security', 'Gym Access', 'Metro Nearby'],
      roommates: 0,
      lastActive: '12 hours ago'
    },
    {
      id: '8',
      title: 'Shared Flat with Garden in Moda',
      type: 'Shared Apartment',
      location: 'Moda, Kadıköy, Istanbul',
      price: 2900,
      currency: 'TRY',
      period: 'month',
      beds: 4,
      baths: 2,
      area: 130,
      image: 'https://source.unsplash.com/random/800x600/?garden,apartment',
      verified: true,
      featured: false,
      available: 'June 1, 2025',
      furnished: true,
      gender: 'mixed',
      description: 'A charming apartment with a private garden in the trendy Moda neighborhood. Currently two friendly roommates looking for two more. The apartment is spacious, bright, and has a fully equipped kitchen and cozy common areas.',
      amenities: ['Garden', 'Wifi', 'Washing Machine', 'Dishwasher', 'BBQ Area', 'Pet Friendly'],
      roommates: 2,
      lastActive: '1 day ago'
    },
    {
      id: '9',
      title: 'Budget Friendly Room for Students',
      type: 'Private Room',
      location: 'Avcılar, Istanbul',
      price: 1200,
      currency: 'TRY',
      period: 'month',
      beds: 1,
      baths: 1,
      area: 15,
      image: 'https://source.unsplash.com/random/800x600/?student,budget',
      verified: false,
      featured: false,
      available: 'July 1, 2025',
      furnished: true,
      gender: 'male',
      description: 'Affordable room perfect for male students in Avcılar, close to Istanbul University. Basic furnishings including bed, desk, and wardrobe. Shared kitchen and bathroom with two other students.',
      amenities: ['Wifi', 'Desk', 'Near University', 'Public Transport'],
      roommates: 2,
      lastActive: '1 week ago'
    },
    {
      id: '10',
      title: 'Seaside Apartment in Bakırköy',
      type: 'Shared Apartment',
      location: 'Bakırköy, Istanbul',
      price: 3800,
      currency: 'TRY',
      period: 'month',
      beds: 3,
      baths: 1,
      area: 95,
      image: 'https://source.unsplash.com/random/800x600/?seaside,apartment',
      verified: true,
      featured: false,
      available: 'Immediately',
      furnished: true,
      gender: 'female',
      description: 'Beautiful apartment with sea views in Bakırköy. Looking for a female roommate to join two working professionals. The apartment is fully furnished, has a balcony overlooking the sea, and is close to shopping centers and public transportation.',
      amenities: ['Sea View', 'Balcony', 'Wifi', 'Air Conditioning', 'Washing Machine', 'Parking'],
      roommates: 2,
      lastActive: '3 days ago'
    },
    {
      id: '11',
      title: 'Artist Loft in Karaköy',
      type: 'Short Stay',
      location: 'Karaköy, Istanbul',
      price: 300,
      currency: 'TRY',
      period: 'night',
      beds: 1,
      baths: 1,
      area: 70,
      image: 'https://source.unsplash.com/random/800x600/?loft,artist',
      verified: true,
      featured: false,
      available: 'Immediately',
      furnished: true,
      gender: 'any',
      description: 'Creative loft space in the artistic hub of Karaköy. Open floor plan with high ceilings, exposed brick, and artistic touches throughout. Perfect for artists, photographers, or creative souls visiting Istanbul.',
      amenities: ['Art Space', 'Wifi', 'Creative Neighborhood', 'Gallery Nearby', 'Historical Building'],
      roommates: 0,
      lastActive: '6 hours ago'
    },
    {
      id: '12',
      title: 'Family-Friendly Apartment in Ataşehir',
      type: 'Shared Apartment',
      location: 'Ataşehir, Istanbul',
      price: 4000,
      currency: 'TRY',
      period: 'month',
      beds: 4,
      baths: 2,
      area: 140,
      image: 'https://source.unsplash.com/random/800x600/?family,apartment',
      verified: true,
      featured: false,
      available: 'June 1, 2025',
      furnished: true,
      gender: 'mixed',
      description: 'Spacious family apartment in the modern district of Ataşehir. Looking for a roommate or small family to share the space. Apartment includes large living areas, well-equipped kitchen, and is located in a secure complex with amenities.',
      amenities: ['Swimming Pool', 'Playground', 'Security', 'Parking', 'Fitness Center', 'Children Friendly'],
      roommates: 3,
      lastActive: '4 days ago'
    }
  ];
  
  export const filterListings = (filters) => {
    return dummyListings.filter(listing => {
      // Filter by type
      if (filters.type && filters.type !== 'all' && listing.type.toLowerCase().indexOf(filters.type.toLowerCase()) === -1) {
        return false;
      }
      
      // Filter by price range
      if (filters.priceMin && listing.price < parseInt(filters.priceMin)) {
        return false;
      }
      if (filters.priceMax && listing.price > parseInt(filters.priceMax)) {
        return false;
      }
      
      // Filter by number of beds
      if (filters.beds && filters.beds !== 'any' && listing.beds < parseInt(filters.beds)) {
        return false;
      }
      
      // Filter by gender preference
      if (filters.gender && filters.gender !== 'any' && listing.gender !== filters.gender && listing.gender !== 'any') {
        return false;
      }
      
      // Filter by furnished status
      if (filters.furnished === 'yes' && !listing.furnished) {
        return false;
      }
      if (filters.furnished === 'no' && listing.furnished) {
        return false;
      }
      
      // Filter by availability
      if (filters.availableNow && listing.available !== 'Immediately') {
        return false;
      }
      
      // Filter by verification status
      if (filters.verified && !listing.verified) {
        return false;
      }
      
      // Filter by search query
      if (filters.q) {
        const query = filters.q.toLowerCase();
        const matchesTitle = listing.title.toLowerCase().includes(query);
        const matchesLocation = listing.location.toLowerCase().includes(query);
        const matchesDescription = listing.description.toLowerCase().includes(query);
        
        if (!matchesTitle && !matchesLocation && !matchesDescription) {
          return false;
        }
      }
      
      return true;
    });
  };
  
  export const featuredListings = dummyListings.filter(listing => listing.featured).slice(0, 6);
  
  export const dummyRoommates = [
    {
      id: '1',
      name: 'Alex Johnson',
      age: 25,
      gender: 'Male',
      occupation: 'Software Engineer',
      image: 'https://source.unsplash.com/random/300x300/?portrait,man,1',
      location: 'Looking in Beşiktaş, Şişli',
      budget: '2000-3000 TRY',
      moveInDate: 'June 1, 2025',
      bio: 'Tech professional working remotely for a US company. I\'m clean, quiet, and respectful. I enjoy cooking, hiking, and occasional social gatherings. Looking for a comfortable place with like-minded roommates.',
      interests: ['Technology', 'Cooking', 'Hiking', 'Photography'],
      languages: ['English', 'Basic Turkish'],
      smoking: false,
      pets: false,
      verified: true,
      lastActive: '2 hours ago'
    },
    {
      id: '2',
      name: 'Sophia Martinez',
      age: 23,
      gender: 'Female',
      occupation: 'Graduate Student',
      image: 'https://source.unsplash.com/random/300x300/?portrait,woman,1',
      location: 'Looking in Kadıköy, Üsküdar',
      budget: '1500-2200 TRY',
      moveInDate: 'May 15, 2025',
      bio: 'International student from Spain studying International Relations. I\'m friendly, organized, and enjoy having meaningful conversations. Looking for a clean and quiet place to focus on my studies.',
      interests: ['Reading', 'Politics', 'Coffee Shops', 'Museums'],
      languages: ['English', 'Spanish', 'Learning Turkish'],
      smoking: false,
      pets: false,
      verified: true,
      lastActive: '1 day ago'
    },
    {
      id: '3',
      name: 'Emre Yılmaz',
      age: 28,
      gender: 'Male',
      occupation: 'Architect',
      image: 'https://source.unsplash.com/random/300x300/?portrait,man,2',
      location: 'Looking in Beyoğlu, Galata',
      budget: '2500-3500 TRY',
      moveInDate: 'Immediately',
      bio: 'Local architect working in the city center. I appreciate good design, cleanliness, and respectful communication. I\'m social but also value my personal space. Looking for a place with character in the historic center.',
      interests: ['Architecture', 'Design', 'Art', 'City Exploration'],
      languages: ['Turkish', 'English', 'Italian'],
      smoking: true,
      pets: false,
      verified: true,
      lastActive: '5 hours ago'
    },
    {
      id: '4',
      name: 'Emma Wilson',
      age: 26,
      gender: 'Female',
      occupation: 'English Teacher',
      image: 'https://source.unsplash.com/random/300x300/?portrait,woman,2',
      location: 'Looking in Şişli, Beşiktaş',
      budget: '2000-2800 TRY',
      moveInDate: 'July 1, 2025',
      bio: 'English teacher from Australia working at a language school. I\'m easy-going, tidy, and enjoy exploring the city. Looking for friendly roommates who enjoy occasional dinners together but respect privacy.',
      interests: ['Traveling', 'Languages', 'Cooking', 'Yoga'],
      languages: ['English', 'Some Turkish'],
      smoking: false,
      pets: true,
      verified: false,
      lastActive: '2 days ago'
    },
    {
      id: '5',
      name: 'Mehmet Kaya',
      age: 24,
      gender: 'Male',
      occupation: 'Medical Student',
      image: 'https://source.unsplash.com/random/300x300/?portrait,man,3',
      location: 'Looking in Fatih, Zeytinburnu',
      budget: '1800-2200 TRY',
      moveInDate: 'June 1, 2025',
      bio: 'Medical student in my final year. I spend most of my time studying and at the hospital. I\'m very organized, quiet, and respectful. Looking for a peaceful place close to public transportation.',
      interests: ['Medicine', 'Reading', 'Swimming', 'Chess'],
      languages: ['Turkish', 'English'],
      smoking: false,
      pets: false,
      verified: true,
      lastActive: '1 week ago'
    },
    {
      id: '6',
      name: 'Aisha Rahman',
      age: 27,
      gender: 'Female',
      occupation: 'Digital Marketer',
      image: 'https://source.unsplash.com/random/300x300/?portrait,woman,3',
      location: 'Looking in Kadıköy, Moda',
      budget: '2200-3000 TRY',
      moveInDate: 'Immediately',
      bio: 'Working remotely as a digital marketer for an international company. I\'m creative, sociable, and love discovering new cafes and restaurants. Looking for a vibrant neighborhood with like-minded roommates.',
      interests: ['Marketing', 'Social Media', 'Cafes', 'Music'],
      languages: ['English', 'Arabic', 'Some Turkish'],
      smoking: false,
      pets: false,
      verified: true,
      lastActive: '3 days ago'
    }
  ];
  
  export const featuredRoommates = dummyRoommates.filter(roommate => roommate.verified).slice(0, 4);