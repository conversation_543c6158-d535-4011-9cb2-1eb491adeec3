import { uploadToS3, generateUniqueFilename, deleteFromS3, getS3KeyFromUrl } from './s3';

/**
 * Upload multiple images for a listing
 * @param {Array} files - Array of file objects
 * @returns {Promise<Array>} - Array of image objects with URLs
 */
export const uploadListingImages = async (files) => {
  if (!files || files.length === 0) {
    return [];
  }

  const uploadPromises = files.map(async (file, index) => {
    const fileName = generateUniqueFilename(file.originalname || `image-${index}.jpg`);
    const url = await uploadToS3(
      file.buffer,
      `listing-images/${fileName}`,
      file.mimetype || 'image/jpeg'
    );

    return {
      url,
      caption: '',
      isMain: index === 0 // First image is the main image by default
    };
  });

  return Promise.all(uploadPromises);
};

/**
 * Delete listing images from S3
 * @param {Array} images - Array of image objects with URLs
 * @returns {Promise<void>}
 */
export const deleteListingImages = async (images) => {
  if (!images || images.length === 0) {
    return;
  }

  const deletePromises = images.map(async (image) => {
    const key = getS3KeyFromUrl(image.url);
    if (key) {
      await deleteFromS3(key);
    }
  });

  return Promise.all(deletePromises);
};

/**
 * Update listing images
 * @param {Array} existingImages - Array of existing image objects
 * @param {Array} newFiles - Array of new file objects
 * @param {Array} imagesToDelete - Array of image URLs to delete
 * @param {String} mainImageUrl - URL of the image to set as main
 * @returns {Promise<Array>} - Updated array of image objects
 */
export const updateListingImages = async (existingImages, newFiles, imagesToDelete, mainImageUrl) => {
  // Create a copy of existing images
  let updatedImages = [...existingImages];

  // Delete images if specified
  if (imagesToDelete && imagesToDelete.length > 0) {
    // Delete from S3
    const imagesToDeleteObjects = updatedImages.filter(img => 
      imagesToDelete.includes(img.url)
    );
    await deleteListingImages(imagesToDeleteObjects);
    
    // Remove from array
    updatedImages = updatedImages.filter(img => 
      !imagesToDelete.includes(img.url)
    );
  }

  // Upload new images if provided
  if (newFiles && newFiles.length > 0) {
    const newImages = await uploadListingImages(newFiles);
    updatedImages = [...updatedImages, ...newImages];
  }

  // Set main image if specified
  if (mainImageUrl) {
    updatedImages = updatedImages.map(img => ({
      ...img,
      isMain: img.url === mainImageUrl
    }));
  } else if (updatedImages.length > 0 && !updatedImages.some(img => img.isMain)) {
    // If no main image is set, set the first one as main
    updatedImages[0].isMain = true;
  }

  return updatedImages;
};
