import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

// AWS S3 configuration
const s3Client = new S3Client({
  region: 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '********************',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '4jSXyjXdvXf/NT0bKfjJOkn3ur2b3NCt2Lc5ILXR',
  },
});

const bucketName = process.env.AWS_BUCKET_NAME || 'roomshare-resources';

/**
 * Generate a unique filename for S3
 * @param {string} originalFilename - Original filename
 * @returns {string} - Unique filename
 */
export const generateUniqueFilename = (originalFilename) => {
  const fileExtension = originalFilename.split('.').pop();
  return `${uuidv4()}.${fileExtension}`;
};

/**
 * Upload a file to S3
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileName - File name
 * @param {string} contentType - File content type
 * @returns {Promise<string>} - S3 file URL
 */
export const uploadToS3 = async (fileBuffer, fileName, contentType) => {
  const params = {
    Bucket: bucketName,
    Key: `profile-images/${fileName}`,
    Body: fileBuffer,
    ContentType: contentType,
  };

  try {
    await s3Client.send(new PutObjectCommand(params));
    return `https://${bucketName}.s3.amazonaws.com/profile-images/${fileName}`;
  } catch (error) {
    console.error('Error uploading to S3:', error);
    throw error;
  }
};

/**
 * Get a signed URL for an S3 object
 * @param {string} key - S3 object key
 * @returns {Promise<string>} - Signed URL
 */
export const getSignedS3Url = async (key) => {
  const params = {
    Bucket: bucketName,
    Key: key,
  };

  try {
    const command = new GetObjectCommand(params);
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
    return signedUrl;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    throw error;
  }
};

/**
 * Delete an object from S3
 * @param {string} key - S3 object key
 * @returns {Promise<void>}
 */
export const deleteFromS3 = async (key) => {
  const params = {
    Bucket: bucketName,
    Key: key,
  };

  try {
    await s3Client.send(new DeleteObjectCommand(params));
  } catch (error) {
    console.error('Error deleting from S3:', error);
    throw error;
  }
};

/**
 * Extract S3 key from URL
 * @param {string} url - S3 URL
 * @returns {string} - S3 key
 */
export const getS3KeyFromUrl = (url) => {
  if (!url) return null;
  const urlObj = new URL(url);
  return urlObj.pathname.substring(1); // Remove leading slash
};
