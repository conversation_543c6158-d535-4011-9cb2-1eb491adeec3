/**
 * Service for interacting with the listings API
 */

// Get all listings with filtering
export const getListings = async (filters = {}) => {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });

    const response = await fetch(`/api/listings?${queryParams.toString()}`);
    const data = await response.json();

    if (response.ok) {
      return { success: true, ...data };
    } else {
      return { success: false, message: data.message || 'Failed to fetch listings' };
    }
  } catch (error) {
    console.error('Error fetching listings:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Get a single listing by ID
export const getListing = async (id) => {
  try {
    const response = await fetch(`/api/listings/${id}`);
    const data = await response.json();

    if (response.ok) {
      return { success: true, data: data.data };
    } else {
      return { success: false, message: data.message || 'Failed to fetch listing' };
    }
  } catch (error) {
    console.error('Error fetching listing:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Create a new listing
export const createListing = async (listingData, images) => {
  try {
    const formData = new FormData();
    formData.append('listingData', JSON.stringify(listingData));

    // Append images if provided
    if (images && images.length > 0) {
      images.forEach(image => {
        formData.append('images', image);
      });
    }

    const token = localStorage.getItem('token');

    const response = await fetch('/api/listings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, data: data.data };
    } else {
      return { success: false, message: data.message || 'Failed to create listing' };
    }
  } catch (error) {
    console.error('Error creating listing:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Update a listing
export const updateListing = async (id, listingData, newImages = [], imagesToDelete = [], mainImageUrl = '') => {
  try {
    const formData = new FormData();
    formData.append('listingData', JSON.stringify(listingData));

    // Append images to delete if provided
    if (imagesToDelete.length > 0) {
      formData.append('imagesToDelete', JSON.stringify(imagesToDelete));
    }

    // Append main image URL if provided
    if (mainImageUrl) {
      formData.append('mainImageUrl', mainImageUrl);
    }

    // Append new images if provided
    if (newImages && newImages.length > 0) {
      newImages.forEach(image => {
        formData.append('images', image);
      });
    }

    const token = localStorage.getItem('token');

    const response = await fetch(`/api/listings/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, data: data.data };
    } else {
      return { success: false, message: data.message || 'Failed to update listing' };
    }
  } catch (error) {
    console.error('Error updating listing:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};



// Delete a listing
export const deleteListing = async (id) => {
  try {
    const token = localStorage.getItem('token');

    if (!token) {
      return { success: false, message: 'Not authenticated' };
    }

    const response = await fetch(`/api/listings/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, message: data.message || 'Listing deleted successfully' };
    } else {
      return { success: false, message: data.message || 'Failed to delete listing' };
    }
  } catch (error) {
    console.error('Error deleting listing:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Save or unsave a listing
export const toggleSaveListing = async (id, action) => {
  try {
    const token = localStorage.getItem('token');

    const response = await fetch(`/api/listings/${id}/save`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ action }),
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, message: data.message, isSaved: data.isSaved };
    } else {
      return { success: false, message: data.message || 'Failed to save/unsave listing' };
    }
  } catch (error) {
    console.error('Error saving/unsaving listing:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Get user's saved listings
export const getSavedListings = async (page = 1, limit = 10) => {
  try {
    const token = localStorage.getItem('token');

    const response = await fetch(`/api/users/saved-listings?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, ...data };
    } else {
      return { success: false, message: data.message || 'Failed to fetch saved listings' };
    }
  } catch (error) {
    console.error('Error fetching saved listings:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};

// Get user's own listings
export const getMyListings = async (status = 'published', page = 1, limit = 10) => {
  try {
    const token = localStorage.getItem('token');

    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);
    if (status) {
      queryParams.append('status', status);
    }

    const response = await fetch(`/api/users/my-listings?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (response.ok) {
      return { success: true, ...data };
    } else {
      return { success: false, message: data.message || 'Failed to fetch your listings' };
    }
  } catch (error) {
    console.error('Error fetching user listings:', error);
    return { success: false, message: 'An error occurred. Please try again.' };
  }
};
