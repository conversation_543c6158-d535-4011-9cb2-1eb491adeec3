// middleware.js
import { withAuth } from "next-auth/middleware";
import { NextResponse } from "next/server";

// Define protected routes and their required roles
const protectedRoutes = {
  // User routes - require authentication
  '/dashboard': ['user', 'lister', 'admin'],
  '/profile': ['user', 'lister', 'admin'],
  '/account': ['user', 'lister', 'admin'],
  '/favorites': ['user', 'lister', 'admin'],
  '/messages': ['user', 'lister', 'admin'],
  '/applications': ['user', 'lister', 'admin'],
  
  // Lister routes - require lister or admin role
  '/dashboard/host': ['lister', 'admin'],
  '/listings/create': ['lister', 'admin'],
  '/listings/manage': ['lister', 'admin'],
  '/listings/edit': ['lister', 'admin'],
  '/bookings': ['lister', 'admin'],
  '/analytics': ['lister', 'admin'],
  
  // Admin routes - require admin role only
  '/admin': ['admin'],
  '/admin/users': ['admin'],
  '/admin/listings': ['admin'],
  '/admin/reports': ['admin'],
  '/admin/moderation': ['admin'],
  '/admin/analytics': ['admin'],
  '/admin/settings': ['admin'],
};

// API routes that require authentication
const protectedApiRoutes = {
  '/api/users': ['user', 'lister', 'admin'],
  '/api/listings/create': ['lister', 'admin'],
  '/api/listings/edit': ['lister', 'admin'],
  '/api/messages': ['user', 'lister', 'admin'],
  '/api/admin': ['admin'],
};

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // Check if the route requires specific roles
    const requiredRoles = protectedRoutes[pathname] || protectedApiRoutes[pathname];
    
    if (requiredRoles) {
      // If user is not authenticated, redirect to login
      if (!token) {
        const loginUrl = new URL('/auth/login', req.url);
        loginUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(loginUrl);
      }

      // Check if user has required role
      const userRole = token.role || 'user';
      if (!requiredRoles.includes(userRole)) {
        // Redirect based on user role
        if (userRole === 'user') {
          return NextResponse.redirect(new URL('/dashboard', req.url));
        } else if (userRole === 'lister') {
          return NextResponse.redirect(new URL('/dashboard/host', req.url));
        } else {
          return NextResponse.redirect(new URL('/unauthorized', req.url));
        }
      }

      // Check email verification for sensitive routes
      if (!token.emailVerified && pathname.startsWith('/admin')) {
        return NextResponse.redirect(new URL('/auth/verify-email', req.url));
      }
    }

    // Handle dynamic routes
    if (pathname.startsWith('/listings/') && pathname.includes('/edit')) {
      const userRole = token?.role || 'user';
      if (!['lister', 'admin'].includes(userRole)) {
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }
    }

    // Redirect authenticated users away from auth pages
    if (token && pathname.startsWith('/auth/')) {
      const userRole = token.role || 'user';
      if (userRole === 'admin') {
        return NextResponse.redirect(new URL('/admin', req.url));
      } else if (userRole === 'lister') {
        return NextResponse.redirect(new URL('/dashboard/host', req.url));
      } else {
        return NextResponse.redirect(new URL('/dashboard', req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Allow access to public routes
        const publicRoutes = [
          '/',
          '/search',
          '/listings',
          '/about',
          '/contact',
          '/terms',
          '/privacy',
          '/how-it-works',
          '/auth/login',
          '/auth/register',
          '/auth/forgot-password',
          '/auth/reset-password',
          '/auth/verify-email',
        ];

        // Check if route is public or starts with public path
        const isPublicRoute = publicRoutes.some(route =>
          pathname === route ||
          (route === '/listings' && pathname.startsWith('/listings/') && !pathname.includes('/edit') && !pathname.includes('/create'))
        );

        // Allow public API routes
        const publicApiRoutes = [
          '/api/listings/search',
          '/api/listings/recommended',
          '/api/geocode'
        ];

        const isPublicApiRoute = publicApiRoutes.some(route =>
          pathname === route || pathname.startsWith(route)
        );

        if (isPublicRoute || isPublicApiRoute) {
          return true;
        }

        // For protected routes, require authentication
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
