import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

const UserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Please provide a first name'],
      maxlength: [50, 'First name cannot be more than 50 characters'],
    },
    lastName: {
      type: String,
      required: [true, 'Please provide a last name'],
      maxlength: [50, 'Last name cannot be more than 50 characters'],
    },
    email: {
      type: String,
      required: [true, 'Please provide an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email',
      ],
    },
    password: {
      type: String,
      required: [true, 'Please provide a password'],
      minlength: [8, 'Password must be at least 8 characters long'],
      select: false, // Don't return password in queries by default
    },
    accountType: {
      type: String,
      enum: ['tenant', 'landlord', 'admin'],
      default: 'tenant',
    },
    role: {
      type: String,
      enum: ['user', 'lister', 'moderator', 'admin'],
      default: 'user',
    },
    authProviders: {
      type: [String],
      enum: ['credentials', 'google', 'facebook'],
      default: [],
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    verificationToken: String,
    verificationTokenExpires: Date,
    resetPasswordToken: String,
    resetPasswordExpires: Date,
    profileImage: {
      type: String,
      default: '',
    },
    bio: {
      type: String,
      default: '',
    },
    phone: {
      type: String,
      default: '',
    },
    address: {
      type: String,
      default: '',
    },
    city: {
      type: String,
      default: '',
    },
    country: {
      type: String,
      default: '',
    },
    occupation: {
      type: String,
      default: '',
    },
    preferences: {
      smoking: {
        type: Boolean,
        default: false,
      },
      pets: {
        type: Boolean,
        default: false,
      },
      gender: {
        type: String,
        enum: ['male', 'female', 'other', ''],
        default: '',
      },
      ageRange: {
        type: String,
        default: '',
      },
    },
    socialLinks: {
      facebook: {
        type: String,
        default: '',
      },
      twitter: {
        type: String,
        default: '',
      },
      instagram: {
        type: String,
        default: '',
      },
      linkedin: {
        type: String,
        default: '',
      },
    },
    emailNotifications: {
      messages: {
        type: Boolean,
        default: true,
      },
      listings: {
        type: Boolean,
        default: true,
      },
      marketing: {
        type: Boolean,
        default: false,
      },
    },
    pushNotifications: {
      messages: {
        type: Boolean,
        default: true,
      },
      listings: {
        type: Boolean,
        default: true,
      },
      system: {
        type: Boolean,
        default: true,
      },
    },
    privacySettings: {
      showProfile: {
        type: Boolean,
        default: true,
      },
      showContact: {
        type: Boolean,
        default: false,
      },
      showSocialLinks: {
        type: Boolean,
        default: false,
      },
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Hash password before saving
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// Method to compare passwords
UserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Method to check if user has specific role
UserSchema.methods.hasRole = function (roles) {
  if (typeof roles === 'string') {
    return this.role === roles;
  }
  return roles.includes(this.role);
};

// Method to check if user has specific permissions based on their role
UserSchema.methods.hasPermission = function (permission) {
  const permissionMap = {
    'user': ['view_own_profile', 'edit_own_profile', 'search_listings', 'save_listings', 'send_messages', 'apply_to_listings'],
    'lister': ['view_own_profile', 'edit_own_profile', 'search_listings', 'save_listings', 'send_messages', 'apply_to_listings', 'create_listings', 'manage_own_listings', 'view_applications', 'manage_bookings'],
    'moderator': ['view_own_profile', 'edit_own_profile', 'search_listings', 'save_listings', 'send_messages', 'moderate_listings', 'approve_listings'],
    'admin': ['view_own_profile', 'edit_own_profile', 'search_listings', 'save_listings', 'send_messages', 'moderate_listings', 'approve_listings', 'manage_users', 'system_settings', 'create_listings', 'manage_all_listings', 'view_all_applications', 'manage_all_bookings']
  };

  return permissionMap[this.role] && permissionMap[this.role].includes(permission);
};

// Generate email verification token
UserSchema.methods.generateVerificationToken = function () {
  // Generate a random token
  const verificationToken = crypto.randomBytes(32).toString('hex');

  // Hash token and set to verificationToken field
  this.verificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  // Set token expiry (24 hours)
  this.verificationTokenExpires = Date.now() + 24 * 60 * 60 * 1000;

  return verificationToken;
};

// Generate password reset token
UserSchema.methods.generatePasswordResetToken = function () {
  // Generate a random token
  const resetToken = crypto.randomBytes(32).toString('hex');

  // Hash token and set to resetPasswordToken field
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set token expiry (1 hour)
  this.resetPasswordExpires = Date.now() + 60 * 60 * 1000;

  return resetToken;
};

// Prevent mongoose from creating a new model if it already exists
export default mongoose.models.User || mongoose.model('User', UserSchema);
