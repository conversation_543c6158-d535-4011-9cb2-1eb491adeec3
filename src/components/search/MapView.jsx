// components/search/MapView.jsx
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Script from 'next/script';

const MapView = ({ listings, userLocation, selectedPlace }) => {
  const [selectedListing, setSelectedListing] = useState(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  const infoWindowRef = useRef(null);
  
  // Generate pins from listings
  const pins = listings.map(listing => {
    // Use actual coordinates if available, otherwise generate random ones
    const hasCoordinates = listing.address && listing.address.location &&
                          listing.address.location.coordinates &&
                          listing.address.location.coordinates.length === 2;

    // Format price for display
    const price = typeof listing.price === 'object' ? listing.price.amount : listing.price;
    const currency = listing.price?.currency || 'PKR';
    const formattedPrice = currency === 'PKR'
      ? `PKR ${Math.floor(price / 1000)}K`
      : `$${Math.floor(price / 1000)}K`;

    // Default to Hyderabad, Pakistan coordinates if no coordinates available
    const defaultLat = 25.3960 + (Math.random() * 0.1 - 0.05); // Hyderabad area
    const defaultLng = 68.3578 + (Math.random() * 0.1 - 0.05);

    return {
      id: listing._id,
      lat: hasCoordinates ? listing.address.location.coordinates[1] : defaultLat,
      lng: hasCoordinates ? listing.address.location.coordinates[0] : defaultLng,
      listing: listing,
      price: formattedPrice,
      distance: listing.distance
    };
  });
  
  // Initialize Google Maps
  const initializeMap = () => {
    if (!window.google || !mapRef.current) return;
    
    // Create map centered on selected place, user location, first listing, or default to Hyderabad
    const mapCenter = selectedPlace
      ? { lat: selectedPlace.lat, lng: selectedPlace.lng }
      : (userLocation
        ? { lat: userLocation.lat, lng: userLocation.lng }
        : (pins.length > 0
            ? { lat: pins[0].lat, lng: pins[0].lng }
            : { lat: 25.3960, lng: 68.3578 })); // Hyderabad, Pakistan
          
    // Determine appropriate zoom level - start a bit zoomed out to show more context
    const zoomLevel = selectedPlace ? 12 : (userLocation ? 11 : 13);
    
    const map = new window.google.maps.Map(mapRef.current, {
      center: mapCenter,
      zoom: zoomLevel,
      mapTypeControl: true,
      mapTypeControlOptions: {
        style: window.google.maps.MapTypeControlStyle.DROPDOWN_MENU,
        position: window.google.maps.ControlPosition.TOP_RIGHT
      },
      fullscreenControl: true,
      fullscreenControlOptions: {
        position: window.google.maps.ControlPosition.RIGHT_TOP
      },
      streetViewControl: true,
      streetViewControlOptions: {
        position: window.google.maps.ControlPosition.RIGHT_TOP
      },
      zoomControl: true,
      zoomControlOptions: {
        position: window.google.maps.ControlPosition.RIGHT_CENTER
      }
    });
    
    // Create info window
    infoWindowRef.current = new window.google.maps.InfoWindow();
    
    // Add markers for all listings
    markersRef.current = pins.map(pin => {
      const marker = new window.google.maps.Marker({
        position: { lat: pin.lat, lng: pin.lng },
        map: map,
        title: pin.listing.title,
        label: {
          text: pin.price,
          color: '#FFFFFF',
          fontWeight: 'bold'
        }
      });
      
      // Add click event
      marker.addListener('click', () => {
        setSelectedListing(pin.listing);
      });
      
      return marker;
    });
    
    // Add user location marker if available
    if (userLocation) {
      new window.google.maps.Marker({
        position: { lat: userLocation.lat, lng: userLocation.lng },
        map: map,
        icon: {
          path: window.google.maps.SymbolPath.CIRCLE,
          scale: 10,
          fillColor: '#4285F4',
          fillOpacity: 0.8,
          strokeColor: '#FFFFFF',
          strokeWeight: 2
        },
        title: 'Your Location'
      });
    }
    
    // Store map instance for later use
    mapInstanceRef.current = map;
    
    setMapLoaded(true);
  };

  // Set up the callback for Google Maps API
  useEffect(() => {
    window.initMap = initializeMap;
    
    // If Google Maps is already loaded, initialize the map
    if (window.google && mapRef.current && !mapLoaded) {
      initializeMap();
    }
    
    // Cleanup markers on unmount
    return () => {
      if (markersRef.current) {
        markersRef.current.forEach(marker => {
          if (marker) marker.setMap(null);
        });
      }
    };
  }, [mapLoaded]);

  // Update markers when listings change
  useEffect(() => {
    if (mapInstanceRef.current && listings.length > 0) {
      // Clear existing markers
      if (markersRef.current) {
        markersRef.current.forEach(marker => {
          if (marker) marker.setMap(null);
        });
      }
      markersRef.current = [];
      
      // Add new markers
      pins.forEach(pin => {
        const marker = new window.google.maps.Marker({
          position: { lat: pin.lat, lng: pin.lng },
          map: mapInstanceRef.current,
          title: pin.listing.title,
          label: {
            text: pin.price,
            color: '#FFFFFF',
            fontWeight: 'bold'
          }
        });
        
        // Add click event
        marker.addListener('click', () => {
          setSelectedListing(pin.listing);
        });
        
        markersRef.current.push(marker);
      });
    }
  }, [listings, mapInstanceRef.current]);

  return (
    <div className="relative rounded-lg overflow-hidden shadow-md bg-white" style={{ height: '600px', width: '100%' }}>
      {/* Google Maps Script */}
      <Script
        src={`https://maps.googleapis.com/maps/api/js?key=AIzaSyClcjOMtmEvRntPDtaV1013Yi10X8VgQw0&libraries=places&callback=initMap`}
        strategy="afterInteractive"
      />
      
      {/* Map Container */}
      <div 
        id="google-map"
        ref={mapRef} 
        className="absolute inset-0" 
        style={{ height: '600px', width: '100%' }} 
      />
      
      {/* Map Controls */}
      <div className="absolute top-4 left-4 z-10 flex justify-between w-auto">
        <div className="bg-white rounded-md shadow-sm p-2">
          <p className="text-gray-700 text-sm">Showing {listings.length} listings in this area</p>
        </div>
        <div className="flex space-x-2">
          <button className="bg-white rounded-md shadow-sm p-2 text-gray-700 hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
          <button className="bg-white rounded-md shadow-sm p-2 text-gray-700 hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
        </div>
      </div>
      
      {/* Loading Placeholder */}
      {!mapLoaded && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-center">
            <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
      
      {/* Info Popup for Selected Listing - Will be shown when a marker is clicked */}
      {selectedListing && (
        <div className="absolute z-20 bg-white rounded-lg shadow-xl p-3 w-64 bottom-4 right-4">
          <Link href={`/listings/${selectedListing._id}`}>
            <div className="flex space-x-3">
              <div className="w-20 h-20 flex-shrink-0 rounded-md overflow-hidden">
                <img 
                  src={selectedListing.images && selectedListing.images.length > 0 ? selectedListing.images[0] : '/images/placeholder.jpg'}
                  alt={selectedListing.title}
                  className="w-full h-full object-cover"
                  onError={(e) => { e.target.src = '/images/placeholder.jpg'; }}
                />
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-bold text-gray-900 line-clamp-2">{selectedListing.title}</h4>
                <p className="text-xs text-gray-600">{selectedListing.address?.city || 'Location not specified'}</p>
                <p className="text-sm font-bold text-indigo-600 mt-1">
                  {typeof selectedListing.price === 'object' 
                    ? `$${selectedListing.price.amount.toLocaleString()}` 
                    : `$${selectedListing.price.toLocaleString()}`}
                  <span className="font-normal">/{selectedListing.period || 'month'}</span>
                </p>
                <button className="text-xs text-blue-600 hover:underline mt-1">View details →</button>
              </div>
            </div>
          </Link>
        </div>
      )}
      
      {/* Custom CSS for markers and map controls */}
      <style jsx global>{`
        .custom-marker {
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .marker-price {
          background: white;
          color: #4f46e5;
          border: 1px solid #4f46e5;
          padding: 4px 8px;
          border-radius: 16px;
          font-weight: bold;
          font-size: 12px;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          transition: all 0.2s;
        }
        
        .marker-price:hover {
          background: #4f46e5;
          color: white;
          transform: scale(1.1);
        }
        
        .view-all-button {
          margin: 10px;
          font-family: 'Arial', sans-serif;
        }
        
        .view-all-button button {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
        }
        
        /* Improve info window styling */
        .gm-style .gm-style-iw-c {
          padding: 12px;
          border-radius: 8px;
        }
        
        .gm-style .gm-style-iw-d {
          overflow: hidden !important;
        }
        
        /* Pulsating animation for user location */
        @keyframes pulse {
          0% {
            transform: scale(0.8);
            opacity: 0.7;
          }
          70% {
            transform: scale(1.5);
            opacity: 0;
          }
          100% {
            transform: scale(0.8);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default MapView;