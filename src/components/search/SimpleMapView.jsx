// components/search/SimpleMapView.jsx
import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Script from 'next/script';

const SimpleMapView = ({ listings, userLocation, selectedPlace }) => {
  const [selectedListing, setSelectedListing] = useState(null);
  const [selectedMarkerPosition, setSelectedMarkerPosition] = useState(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const markersRef = useRef([]);
  const infoWindowRef = useRef(null);

  // Format listings for map display
  const pins = listings.map(listing => {
    const hasCoordinates = listing.address &&
                          listing.address.location &&
                          listing.address.location.coordinates &&
                          listing.address.location.coordinates.length === 2;

    const price = typeof listing.price === 'object' ? listing.price.amount : listing.price;
    const currency = listing.price?.currency || 'PKR';
    const formattedPrice = currency === 'PKR'
      ? `PKR ${Math.floor(price / 1000)}K`
      : `$${Math.floor(price / 1000)}K`;

    // Debug coordinates
    if (hasCoordinates) {
      console.log('Listing coordinates:', listing.address.location.coordinates);
    }

    // Generate coordinates near user location or default to Hyderabad area
    const defaultLat = userLocation ? userLocation.lat + (Math.random() * 0.1 - 0.05) : 25.3960 + (Math.random() * 0.1 - 0.05);
    const defaultLng = userLocation ? userLocation.lng + (Math.random() * 0.1 - 0.05) : 68.3578 + (Math.random() * 0.1 - 0.05);

    return {
      id: listing._id,
      lat: hasCoordinates ? listing.address.location.coordinates[1] : defaultLat,
      lng: hasCoordinates ? listing.address.location.coordinates[0] : defaultLng,
      listing: listing,
      price: formattedPrice,
      distance: listing.distance
    };
  });
  
  // Debug pins
  console.log('Total pins:', pins.length);
  if (pins.length > 0) {
    console.log('First pin coordinates:', pins[0].lat, pins[0].lng);
  }
  if (userLocation) {
    console.log('User location:', userLocation.lat, userLocation.lng);
  }

  // Close info window when clicking elsewhere on the map
  useEffect(() => {
    if (mapInstanceRef.current && selectedListing) {
      const listener = window.google.maps.event.addListener(mapInstanceRef.current, 'click', () => {
        // Close info window
        if (infoWindowRef.current) {
          infoWindowRef.current.close();
        }
        
        // Reset state
        setSelectedListing(null);
        setSelectedMarkerPosition(null);
      });
      
      return () => {
        window.google.maps.event.removeListener(listener);
      };
    }
  }, [mapInstanceRef.current, selectedListing]);

  // Initialize map when Google Maps API is loaded or when selectedPlace/userLocation changes
  useEffect(() => {
    // Clear previous map instance if it exists
    if (mapInstanceRef.current) {
      // Clear markers
      if (markersRef.current) {
        markersRef.current.forEach(marker => {
          if (marker) marker.setMap(null);
        });
      }
      mapInstanceRef.current = null;
    }
    // Define global callback for Google Maps
    window.initMap = function() {
      console.log('initMap called, mapRef:', !!mapRef.current, 'mapInstance:', !!mapInstanceRef.current);
      if (!mapRef.current || mapInstanceRef.current) return;
      
      // Create map centered on selected place, user location, or first listing
      const mapCenter = selectedPlace
        ? { lat: selectedPlace.lat, lng: selectedPlace.lng }
        : (userLocation 
          ? { lat: userLocation.lat, lng: userLocation.lng } 
          : (pins.length > 0 
              ? { lat: pins[0].lat, lng: pins[0].lng } 
              : { lat: 27.5382272, lng: 68.2033152 })); // Default to Hyderabad, Pakistan
      
      console.log('Map center:', mapCenter);
      
      // Create map instance
      const map = new window.google.maps.Map(mapRef.current, {
        center: mapCenter,
        zoom: selectedPlace ? 12 : 13,
        mapTypeControl: true,
        mapTypeControlOptions: {
          position: window.google.maps.ControlPosition.TOP_RIGHT
        },
        fullscreenControl: true,
        fullscreenControlOptions: {
          position: window.google.maps.ControlPosition.RIGHT_TOP
        },
        streetViewControl: true,
        streetViewControlOptions: {
          position: window.google.maps.ControlPosition.RIGHT_TOP
        },
        zoomControl: true,
        zoomControlOptions: {
          position: window.google.maps.ControlPosition.RIGHT_CENTER
        }
      });
      
      // Create info window for markers
      infoWindowRef.current = new window.google.maps.InfoWindow();
      
      // Store map instance
      mapInstanceRef.current = map;
      
      // Add markers for listings
      console.log('Adding', pins.length, 'markers to map');
      const markers = pins.map(pin => {
        console.log('Creating marker at:', pin.lat, pin.lng);
        const marker = new window.google.maps.Marker({
          position: { lat: pin.lat, lng: pin.lng },
          map: map,
          title: pin.listing.title,
          label: {
            text: pin.price,
            color: '#FFFFFF',
            fontWeight: 'bold'
          }
        });
        
        // Add click event
        marker.addListener('click', () => {
          // Update state
          setSelectedListing(pin.listing);
          setSelectedMarkerPosition({ lat: pin.lat, lng: pin.lng });
          
          // Create info window content
          const infoContent = `
            <div class="p-3 max-w-xs info-window-content">
              <div class="flex space-x-3">
                <div class="w-20 h-20 flex-shrink-0 rounded-md overflow-hidden">
                  <img 
                    src="${pin.listing.images && pin.listing.images.length > 0 ? pin.listing.images[0] : '/images/placeholder.jpg'}"
                    alt="${pin.listing.title}"
                    class="w-full h-full object-cover"
                    onerror="this.src='/images/placeholder.jpg';"
                  />
                </div>
                <div class="flex-1">
                  <h4 class="text-sm font-bold text-gray-900">${pin.listing.title}</h4>
                  <p class="text-xs text-gray-600">${pin.listing.address?.city || 'Location not specified'}</p>
                  <p class="text-sm font-bold text-indigo-600 mt-1">
                    ${typeof pin.listing.price === 'object' 
                      ? `$${pin.listing.price.amount.toLocaleString()}` 
                      : `$${pin.listing.price.toLocaleString()}`}
                    <span class="font-normal">/${pin.listing.period || 'month'}</span>
                  </p>
                  <a href="/listings/${pin.listing._id}" class="text-xs text-blue-600 hover:underline mt-1 inline-block">View details →</a>
                </div>
              </div>
            </div>
          `;
          
          // Close any open info windows
          if (infoWindowRef.current) {
            infoWindowRef.current.close();
          }
          
          // Set content and open the info window
          infoWindowRef.current.setContent(infoContent);
          infoWindowRef.current.open(map, marker);
        });
        
        return marker;
      });
      
      markersRef.current = markers;
      
      // Add user location marker if available and no specific place is selected
      if (userLocation && !selectedPlace) {
        new window.google.maps.Marker({
          position: { lat: userLocation.lat, lng: userLocation.lng },
          map: map,
          icon: {
            path: window.google.maps.SymbolPath.CIRCLE,
            scale: 10,
            fillColor: '#4285F4',
            fillOpacity: 0.8,
            strokeColor: '#FFFFFF',
            strokeWeight: 2
          },
          title: 'Your Location'
        });
      }
      
      // Fit bounds to include all markers
      if (pins.length > 0) {
        const bounds = new window.google.maps.LatLngBounds();
        pins.forEach(pin => {
          bounds.extend({ lat: pin.lat, lng: pin.lng });
        });
        
        // Only include user location in bounds if no specific place is selected
        if (userLocation && !selectedPlace) {
          bounds.extend({ lat: userLocation.lat, lng: userLocation.lng });
        }
        
        if (selectedPlace) {
          bounds.extend({ lat: selectedPlace.lat, lng: selectedPlace.lng });
          // Add a marker for the selected place
          new window.google.maps.Marker({
            position: { lat: selectedPlace.lat, lng: selectedPlace.lng },
            map: map,
            icon: {
              path: window.google.maps.SymbolPath.CIRCLE,
              scale: 8,
              fillColor: '#FF5722',
              fillOpacity: 0.8,
              strokeColor: '#FFFFFF',
              strokeWeight: 2
            },
            title: 'Selected Location'
          });
          
          // When a place is selected, prioritize it for map centering
          if (pins.length <= 3) {
            // If there are few listings, center on the selected place
            map.setCenter({ lat: selectedPlace.lat, lng: selectedPlace.lng });
            map.setZoom(12); // Set an appropriate zoom level
          }
        }
        
        map.fitBounds(bounds);
      } else if (selectedPlace) {
        // If no pins but we have a selected place, center on it
        map.setCenter({ lat: selectedPlace.lat, lng: selectedPlace.lng });
        map.setZoom(12);
      }
      
      setMapLoaded(true);
    };
    
    // If Google Maps is already loaded, initialize map
    if (window.google && mapRef.current && !mapInstanceRef.current) {
      window.initMap();
    }
    
    // Cleanup markers on unmount
    return () => {
      if (markersRef.current) {
        markersRef.current.forEach(marker => {
          if (marker) marker.setMap(null);
        });
      }
    };
  }, [selectedPlace, userLocation, pins]);

  return (
    <div className="relative rounded-lg overflow-hidden shadow-md bg-white" style={{ height: '600px', width: '100%' }}>
      {/* Google Maps Script */}
      <Script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyClcjOMtmEvRntPDtaV1013Yi10X8VgQw0&libraries=places&callback=initMap"
        strategy="afterInteractive"
      />
      
      {/* Map Container */}
      <div 
        id="google-map"
        ref={mapRef} 
        style={{ height: '600px', width: '100%', position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }} 
      />
      
      {/* Map Controls */}
      <div className="absolute top-4 left-4 z-10 flex justify-between w-auto">
        <div className="bg-white rounded-md shadow-sm p-2">
          <p className="text-gray-700 text-sm">Showing {listings.length} listings in this area</p>
        </div>
      </div>
      
      {/* Loading Placeholder */}
      {!mapLoaded && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center">
          <div className="text-center">
            <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      )}
      
      {/* Custom styles for info windows */}
      <style jsx global>{`
        /* Style the Google Maps info window */
        .gm-style .gm-style-iw-c {
          padding: 0 !important;
          border-radius: 8px !important;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
          overflow: visible !important;
        }
        
        .gm-style .gm-style-iw-d {
          overflow: hidden !important;
          padding: 0 !important;
        }
        
        .gm-style .gm-style-iw-t::after {
          background: linear-gradient(45deg, rgba(255, 255, 255, 1) 50%, rgba(255, 255, 255, 0) 51%, rgba(255, 255, 255, 0) 100%) !important;
        }
        
        .info-window-content {
          width: 300px;
          max-width: 300px;
        }
        
        /* Remove the default close button and add our own */
        .gm-style-iw-c button.gm-ui-hover-effect {
          top: 0 !important;
          right: 0 !important;
          background-color: white !important;
          border-radius: 50% !important;
          margin: 5px !important;
          opacity: 0.8 !important;
        }
        
        .gm-style-iw-c button.gm-ui-hover-effect:hover {
          opacity: 1 !important;
        }
      `}</style>
    </div>
  );
};

export default SimpleMapView;
