// components/search/FilterPanel.jsx
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import Script from 'next/script';

const FilterPanel = ({ initialFilters = {}, onFilterChange = () => {} }) => {
  const router = useRouter();
  const autocompleteRef = useRef(null);
  const cityInputRef = useRef(null);
  
  const [selectedPlace, setSelectedPlace] = useState(null);
  const [filters, setFilters] = useState({
    type: initialFilters.type || 'all',
    priceMin: initialFilters.priceMin || '',
    priceMax: initialFilters.priceMax || '',
    beds: initialFilters.beds || 'any',
    gender: initialFilters.gender || 'any',
    furnished: initialFilters.furnished || 'any',
    availableNow: initialFilters.availableNow || false,
    verified: initialFilters.verified || false,
    city: initialFilters.city || '',
    neighborhood: initialFilters.neighborhood || '',
    ...initialFilters
  });

  const [isExpanded, setIsExpanded] = useState(false);
  const [placesLoaded, setPlacesLoaded] = useState(false);

  // Initialize Google Places Autocomplete
  useEffect(() => {
    if (window.google && window.google.maps && window.google.maps.places && cityInputRef.current && !autocompleteRef.current) {
      const options = {
        types: ['(cities)'],
        fields: ['address_components', 'formatted_address', 'geometry', 'name']
      };
      
      autocompleteRef.current = new window.google.maps.places.Autocomplete(cityInputRef.current, options);
      
      // Add listener for place selection
      autocompleteRef.current.addListener('place_changed', () => {
        const place = autocompleteRef.current.getPlace();
        
        if (place && place.geometry && place.geometry.location) {
          const lat = place.geometry.location.lat();
          const lng = place.geometry.location.lng();
          
          // Get city name from place
          let cityName = '';
          if (place.address_components) {
            for (const component of place.address_components) {
              if (component.types.includes('locality') || component.types.includes('administrative_area_level_1')) {
                cityName = component.long_name;
                break;
              }
            }
          }
          
          if (!cityName && place.name) {
            cityName = place.name;
          }
          
          // Update filters with city and coordinates
          setFilters(prev => ({
            ...prev,
            city: cityName,
            lat: lat,
            lng: lng,
            formattedAddress: place.formatted_address || cityName
          }));
          
          setSelectedPlace({
            lat,
            lng,
            address: place.formatted_address || cityName
          });
          
          // Notify parent component immediately
          onFilterChange({
            ...filters,
            city: cityName,
            lat: lat,
            lng: lng,
            formattedAddress: place.formatted_address || cityName
          });
        }
      });
    }
    
    return () => {
      // Clean up
      if (autocompleteRef.current && window.google) {
        window.google.maps.event.clearInstanceListeners(autocompleteRef.current);
        autocompleteRef.current = null;
      }
    };
  }, [placesLoaded, cityInputRef.current]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    // If city is being changed manually, reset the coordinates
    if (name === 'city' && selectedPlace) {
      setSelectedPlace(null);
    }
    
    setFilters(prev => ({
      ...prev,
      [name]: newValue
    }));
  };

  const handleApplyFilters = () => {
    // Convert the filters to query params for URL
    const queryParams = { ...router.query };

    // Add each filter to the query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'any' && value !== '' && value !== false) {
        queryParams[key] = value;
      } else {
        delete queryParams[key];
      }
    });

    // Handle amenity filters specifically
    const amenityFilters = ['wifi', 'airConditioning', 'parking', 'petsAllowed'];
    const selectedAmenities = amenityFilters.filter(amenity => filters[amenity]);
    if (selectedAmenities.length > 0) {
      queryParams.amenities = selectedAmenities.join(',');
    } else {
      delete queryParams.amenities;
    }

    // If we have a selected place with coordinates, add them to the query params
    if (selectedPlace) {
      queryParams.lat = selectedPlace.lat;
      queryParams.lng = selectedPlace.lng;
    }

    // Update the URL with the new filters
    router.push({
      pathname: router.pathname,
      query: queryParams,
    }, undefined, { shallow: true });

    // Notify parent component with filters and selected place
    onFilterChange({
      ...filters,
      selectedPlace: selectedPlace
    });
  };

  const handleResetFilters = () => {
    const resetFilters = {
      type: 'all',
      priceMin: '',
      priceMax: '',
      beds: 'any',
      gender: 'any',
      furnished: 'any',
      availableNow: false,
      verified: false,
      city: '',
      neighborhood: ''
    };
    
    setFilters(resetFilters);
    
    // Update URL to remove filter params
    router.push({
      pathname: router.pathname,
      query: { q: router.query.q }, // Preserve search query if any
    }, undefined, { shallow: true });
    
    // Notify parent component
    onFilterChange(resetFilters);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Filters</h3>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
          >
            {isExpanded ? (
              <>
                <span>Show Less</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </>
            ) : (
              <>
                <span>Show More</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </>
            )}
          </button>
        </div>
      </div>
      
      <div className="p-4 space-y-6">
        {/* Google Maps Script */}
        <Script
          src={`https://maps.googleapis.com/maps/api/js?key=AIzaSyClcjOMtmEvRntPDtaV1013Yi10X8VgQw0&libraries=places`}
          strategy="afterInteractive"
          onLoad={() => setPlacesLoaded(true)}
        />
        
        {/* Location Filters */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
          <div className="relative">
            <input
              type="text"
              name="city"
              ref={cityInputRef}
              value={filters.city}
              onChange={handleInputChange}
              placeholder="e.g., Istanbul, London, New York"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
            />
            {selectedPlace && (
              <div className="mt-1 text-xs text-gray-500">
                <span className="font-medium">Selected:</span> {selectedPlace.address}
              </div>
            )}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Neighborhood</label>
          <input
            type="text"
            name="neighborhood"
            value={filters.neighborhood}
            onChange={handleInputChange}
            placeholder="e.g., Downtown, Kadıköy, Beyoğlu"
            className="mt-1 block w-full px-3 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          />
        </div>
        
        {/* Listing Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Listing Type</label>
          <select
            name="type"
            value={filters.type}
            onChange={handleInputChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="all">All Types</option>
            <option value="shared">Shared Apartment</option>
            <option value="private">Private Room</option>
            <option value="short">Short Stay</option>
            <option value="roommate">Find Roommate</option>
          </select>
        </div>
        
        {/* Price Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Price Range (TRY)</label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <input
                type="number"
                name="priceMin"
                placeholder="Min"
                value={filters.priceMin}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <input
                type="number"
                name="priceMax"
                placeholder="Max"
                value={filters.priceMax}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
        
        {/* Bedrooms */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
          <select
            name="beds"
            value={filters.beds}
            onChange={handleInputChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="any">Any</option>
            <option value="1">1 or more</option>
            <option value="2">2 or more</option>
            <option value="3">3 or more</option>
            <option value="4">4 or more</option>
          </select>
        </div>
        
        {/* Verification Status - Checkbox */}
        <div className="flex items-center">
          <input
            id="verified"
            name="verified"
            type="checkbox"
            checked={filters.verified}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="verified" className="ml-2 block text-sm text-gray-900">
            Verified Listings Only
          </label>
        </div>
        
        {/* Available Now - Checkbox */}
        <div className="flex items-center">
          <input
            id="availableNow"
            name="availableNow"
            type="checkbox"
            checked={filters.availableNow}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="availableNow" className="ml-2 block text-sm text-gray-900">
            Available Now
          </label>
        </div>
        
        {/* Additional filters that show when expanded */}
        {isExpanded && (
          <div className="space-y-6 pt-4 border-t border-gray-200">
            {/* Gender Preference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Gender Preference</label>
              <select
                name="gender"
                value={filters.gender}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="any">Any</option>
                <option value="male">Male Only</option>
                <option value="female">Female Only</option>
                <option value="mixed">Mixed Gender</option>
              </select>
            </div>
            
            {/* Furnished Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Furnished Status</label>
              <select
                name="furnished"
                value={filters.furnished}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="any">Any</option>
                <option value="yes">Furnished</option>
                <option value="no">Unfurnished</option>
              </select>
            </div>
            
            {/* Additional Amenities */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Amenities</label>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="wifi"
                    name="wifi"
                    type="checkbox"
                    checked={filters.wifi || false}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="wifi" className="ml-2 block text-sm text-gray-900">
                    WiFi
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="airConditioning"
                    name="airConditioning"
                    type="checkbox"
                    checked={filters.airConditioning || false}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="airConditioning" className="ml-2 block text-sm text-gray-900">
                    Air Conditioning
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="parking"
                    name="parking"
                    type="checkbox"
                    checked={filters.parking || false}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="parking" className="ml-2 block text-sm text-gray-900">
                    Parking
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="petsAllowed"
                    name="petsAllowed"
                    type="checkbox"
                    checked={filters.petsAllowed || false}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="petsAllowed" className="ml-2 block text-sm text-gray-900">
                    Pets Allowed
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <button
            onClick={handleResetFilters}
            className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Reset All
          </button>
          <button
            onClick={handleApplyFilters}
            className="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;