// components/common/TrustScore.jsx
import { useState } from 'react';

const TrustScore = ({ score, verifications = [] }) => {
  const [showDetails, setShowDetails] = useState(false);
  
  // Calculate color based on score
  const getScoreColor = () => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };
  
  // Calculate the circumference
  const radius = 30;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (score / 100) * circumference;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold text-gray-900">Trust Score</h3>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-blue-600 hover:text-blue-800 text-sm"
        >
          {showDetails ? 'Hide Details' : 'View Details'}
        </button>
      </div>
      
      <div className="flex items-center justify-center mb-4">
        <div className="relative inline-flex items-center justify-center">
          <svg className="w-20 h-20" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r={radius}
              fill="none"
              stroke="#e5e7eb"
              strokeWidth="8"
            />
            {/* Progress circle */}
            <circle
              cx="50"
              cy="50"
              r={radius}
              fill="none"
              stroke="currentColor"
              strokeWidth="8"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className={getScoreColor()}
              transform="rotate(-90 50 50)"
            />
          </svg>
          <div className="absolute text-xl font-bold">{score}%</div>
        </div>
      </div>
      
      {showDetails && (
        <div className="space-y-3 mt-4">
          <h4 className="font-medium text-gray-700">Verifications</h4>
          <ul className="space-y-2">
            {verifications.map((verification) => (
              <li key={verification.type} className="flex items-center">
                {verification.verified ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
                <span className="text-gray-700">{verification.type}</span>
                {!verification.verified && (
                  <button className="ml-auto text-blue-600 text-sm hover:text-blue-800">
                    Verify
                  </button>
                )}
              </li>
            ))}
          </ul>
          
          <div className="pt-3 border-t border-gray-200">
            <p className="text-sm text-gray-600">
              A higher trust score increases your chances of finding roommates or getting responses to your inquiries.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrustScore;