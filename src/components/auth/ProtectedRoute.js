import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '../../utils/authContext';

const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Set isClient to true when component mounts (client-side only)
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Only redirect if we're on the client and authentication check is complete
    if (isClient && !loading && !user) {
      console.log('No authenticated user found, redirecting to login...');
      router.push('/auth/login');
    }
  }, [loading, user, router, isClient]);

  // Show loading spinner while checking authentication
  if (loading || !isClient || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // User is authenticated, render children
  console.log('User authenticated, rendering protected content');
  return children;
};

export default ProtectedRoute;
