// components/dashboard/SavedListings.jsx
import { useState } from 'react';
import Link from 'next/link';

const SavedListings = ({ listings = [] }) => {
  const [viewMode, setViewMode] = useState('grid');
  const [activeCollection, setActiveCollection] = useState('all');
  
  // Filter listings based on collection
  const filteredListings = activeCollection === 'all' 
    ? listings 
    : listings.filter(listing => listing.collection === activeCollection);
  
  // Get unique collections
  const collections = ['all', ...new Set(listings.map(listing => listing.collection).filter(Boolean))];
  
  // Remove a listing from saved
  const handleRemoveListing = (id) => {
    // In a real app, this would make an API call
    console.log('Removing listing:', id);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-900">Saved Listings</h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${
                viewMode === 'grid' 
                  ? 'bg-gray-100 text-gray-800' 
                  : 'text-gray-500 hover:bg-gray-50'
              }`}
              title="Grid View"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${
                viewMode === 'list' 
                  ? 'bg-gray-100 text-gray-800' 
                  : 'text-gray-500 hover:bg-gray-50'
              }`}
              title="List View"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      {/* Collection Tabs */}
      {collections.length > 1 && (
        <div className="px-6 pt-4 border-b border-gray-200">
          <div className="flex space-x-4 overflow-x-auto pb-2 hide-scrollbar">
            {collections.map((collection) => (
              <button
                key={collection}
                onClick={() => setActiveCollection(collection)}
                className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap ${
                  activeCollection === collection
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {collection === 'all' ? 'All Saved' : collection}
              </button>
            ))}
            <button
              className="px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap bg-gray-50 text-gray-600 hover:bg-gray-100 border border-dashed border-gray-300"
            >
              + New Collection
            </button>
          </div>
        </div>
      )}
      
      {/* Empty State */}
      {filteredListings.length === 0 && (
        <div className="p-6 text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
            <svg className="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
          </div>
          <h3 className="mt-3 text-lg font-medium text-gray-900">No saved listings</h3>
          <p className="mt-2 text-sm text-gray-500">
            {activeCollection === 'all' 
              ? "You haven't saved any listings yet. Browse listings and click the heart icon to save them for later."
              : `You don't have any listings saved in this collection.`}
          </p>
          <div className="mt-6">
            <Link 
              href="/search" 
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Browse Listings
            </Link>
          </div>
        </div>
      )}
      
      {/* Grid View */}
      {filteredListings.length > 0 && viewMode === 'grid' && (
        <div className="p-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredListings.map((listing) => (
            <div key={listing.id} className="group relative bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 hover:shadow-md transition-shadow">
              <Link href={`/listings/${listing.id}`} className="block">
                {/* Image with price overlay */}
                <div className="relative h-48">
                  <div 
                    className="h-full w-full bg-cover bg-center" 
                    style={{ backgroundImage: `url(${listing.image})` }}
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-gray-900 to-transparent h-20" />
                  <div className="absolute bottom-2 left-2 text-white font-bold text-lg">
                    {listing.price.toLocaleString()} {listing.currency}/{listing.period}
                  </div>
                  <div className="absolute top-2 right-2 px-2 py-1 bg-white text-xs font-bold rounded-md text-gray-800">
                    {listing.type}
                  </div>
                  {listing.verified && (
                    <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-md flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Verified
                    </div>
                  )}
                </div>
                
                {/* Listing details */}
                <div className="p-4">
                  <h3 className="text-md font-bold text-gray-900 line-clamp-1">{listing.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{listing.location}</p>
                  <div className="flex mt-2 text-xs text-gray-500 justify-between">
                    <div className="flex space-x-2">
                      <span>{listing.beds} {listing.beds === 1 ? 'Bed' : 'Beds'}</span>
                      <span>•</span>
                      <span>{listing.baths} {listing.baths === 1 ? 'Bath' : 'Baths'}</span>
                      <span>•</span>
                      <span>{listing.area} m²</span>
                    </div>
                    <span>
                      Saved {listing.savedDate}
                    </span>
                  </div>
                </div>
              </Link>
              
              {/* Hover Actions */}
              <div className="absolute top-0 right-0 p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => handleRemoveListing(listing.id)}
                  className="bg-white rounded-full p-2 shadow hover:bg-gray-100"
                  title="Remove from saved"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* List View */}
      {filteredListings.length > 0 && viewMode === 'list' && (
        <div className="divide-y divide-gray-200">
          {filteredListings.map((listing) => (
            <div key={listing.id} className="p-6 hover:bg-gray-50">
              <div className="flex flex-col sm:flex-row">
                {/* Image */}
                <Link href={`/listings/${listing.id}`} className="sm:w-48 h-32 mb-4 sm:mb-0 flex-shrink-0 relative">
                  <div 
                    className="h-full w-full bg-cover bg-center rounded-md" 
                    style={{ backgroundImage: `url(${listing.image})` }}
                  />
                  {listing.verified && (
                    <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-md flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Verified
                    </div>
                  )}
                </Link>
                
                {/* Details */}
                <div className="sm:ml-6 flex-1">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
                    <div>
                      <Link href={`/listings/${listing.id}`}>
                        <h3 className="text-lg font-bold text-gray-900 hover:text-blue-600">{listing.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{listing.location}</p>
                      </Link>
                      
                      <div className="mt-2 flex flex-wrap gap-y-1 gap-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                          </svg>
                          {listing.beds} {listing.beds === 1 ? 'Bed' : 'Beds'}
                        </span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                          </svg>
                          {listing.baths} {listing.baths === 1 ? 'Bath' : 'Baths'}
                        </span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                          </svg>
                          {listing.area} m²
                        </span>
                        <span className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Available: {listing.available}
                        </span>
                      </div>
                    </div>
                    
                    <div className="mt-3 sm:mt-0 text-right">
                      <p className="text-lg font-bold text-blue-600">
                        {listing.price.toLocaleString()} {listing.currency}
                        <span className="text-sm text-gray-600 font-normal">/{listing.period}</span>
                      </p>
                      <div className="mt-1 text-xs text-gray-500">
                        <span>Saved {listing.savedDate}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Actions */}
                  <div className="mt-4 flex flex-wrap gap-2 sm:justify-end">
                    <Link 
                      href={`/listings/${listing.id}`}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      View Listing
                    </Link>
                    <button 
                      onClick={() => {/* Add to collection */}}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z" />
                      </svg>
                      Move to Collection
                    </button>
                    <button 
                      onClick={() => handleRemoveListing(listing.id)}
                      className="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Remove
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SavedListings;