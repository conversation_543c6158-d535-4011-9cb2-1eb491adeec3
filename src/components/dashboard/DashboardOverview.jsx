// components/dashboard/DashboardOverview.jsx
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { dummyListings, dummyRoommates } from '../../utils/dummyData';

const DashboardOverview = ({ userData }) => {
  // For demo purposes, we'll create some dummy data
  const [stats, setStats] = useState({
    activeListings: 2,
    savedListings: 5,
    unreadMessages: 3,
    profileViews: 24
  });

  const [recentListings, setRecentListings] = useState([]);

  // Simulate user data
  useEffect(() => {
    // Get a random selection of listings to show as "saved"
    const savedListings = dummyListings
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);

    setRecentListings(savedListings);
  }, []);

  return (
    <div className="space-y-6">
      {/* Welcome Message */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            {userData && userData.profileImage ? (
              <div className="h-16 w-16 rounded-full overflow-hidden">
                <img
                  src={userData.profileImage}
                  alt={userData.name || 'Profile'}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-teal-400 flex items-center justify-center text-white text-2xl font-bold">
                {userData && userData.name ? userData.name.charAt(0) : 'G'}
              </div>
            )}
          </div>
          <div className="ml-4">
            <h1 className="text-2xl font-bold text-gray-900">Welcome back, {userData ? userData.name : 'Guest'}</h1>
            <p className="text-gray-600">Here's what's happening with your account today.</p>
            <div className="mt-2">
              <a
                href="/dashboard/edit-profile"
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                Edit Profile
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Active Listings</h2>
              <p className="text-2xl font-bold text-gray-900">{stats.activeListings}</p>
            </div>
          </div>
          <div className="mt-3">
            <Link href="/dashboard/listings" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-indigo-100 text-indigo-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Saved Listings</h2>
              <p className="text-2xl font-bold text-gray-900">{stats.savedListings}</p>
            </div>
          </div>
          <div className="mt-3">
            <Link href="/dashboard/saved" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Unread Messages</h2>
              <p className="text-2xl font-bold text-gray-900">{stats.unreadMessages}</p>
            </div>
          </div>
          <div className="mt-3">
            <Link href="/dashboard/messages" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View all
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-pink-100 text-pink-600">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Profile Views</h2>
              <p className="text-2xl font-bold text-gray-900">{stats.profileViews}</p>
            </div>
          </div>
          <div className="mt-3">
            <Link href="/dashboard/roommate-profile" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
              View profile
            </Link>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-bold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/listings/create" className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
            <div className="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <span className="text-gray-700 font-medium">Add New Listing</span>
          </Link>

          <Link href="/roommates/create-profile" className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
            <div className="p-2 rounded-full bg-green-100 text-green-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <span className="text-gray-700 font-medium">Update Profile</span>
          </Link>

          <Link href="/dashboard/messages" className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
            <div className="p-2 rounded-full bg-indigo-100 text-indigo-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <span className="text-gray-700 font-medium">Check Messages</span>
          </Link>

          <Link href="/search" className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
            <div className="p-2 rounded-full bg-orange-100 text-orange-600 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <span className="text-gray-700 font-medium">Browse Listings</span>
          </Link>
        </div>
      </div>

      {/* Recently Saved Listings */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-bold text-gray-900">Recently Saved</h2>
          <Link href="/dashboard/saved" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            View all
          </Link>
        </div>

        <div className="space-y-4">
          {recentListings.length > 0 ? (
            recentListings.map(listing => (
              <div key={listing.id} className="flex border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                <div className="w-20 h-20 bg-gray-200 rounded-md overflow-hidden flex-shrink-0">
                  <div
                    className="w-full h-full bg-cover bg-center"
                    style={{ backgroundImage: `url(${listing.image})` }}
                  />
                </div>
                <div className="ml-4 flex-1">
                  <h3 className="font-medium text-gray-900 line-clamp-1">{listing.title}</h3>
                  <p className="text-sm text-gray-600">{listing.location}</p>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-sm font-bold text-blue-600">{listing.price.toLocaleString()} {listing.currency}/{listing.period}</p>
                    <Link href={`/listings/${listing.id}`} className="text-xs text-blue-600 hover:text-blue-800">
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-6 text-gray-500">
              <p>No saved listings yet</p>
              <Link href="/search" className="text-blue-600 hover:text-blue-800 font-medium mt-2 inline-block">
                Browse listings
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;