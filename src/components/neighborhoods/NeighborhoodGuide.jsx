// components/neighborhoods/NeighborhoodGuide.jsx
import { useState } from 'react';

const NeighborhoodGuide = ({ neighborhood }) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header with neighborhood image */}
      <div className="relative h-64">
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{ backgroundImage: `url(${neighborhood.image})` }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent"></div>
        <div className="absolute bottom-0 left-0 p-6">
          <h2 className="text-3xl font-bold text-white">{neighborhood.name}</h2>
          <p className="text-white text-lg">{neighborhood.subtitle}</p>
        </div>
      </div>
      
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'overview'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('living')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'living'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Living Here
          </button>
          <button
            onClick={() => setActiveTab('transport')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'transport'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Transportation
          </button>
          <button
            onClick={() => setActiveTab('prices')}
            className={`py-4 px-6 text-sm font-medium ${
              activeTab === 'prices'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Prices
          </button>
        </nav>
      </div>
      
      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div>
            <p className="text-gray-700 mb-4">{neighborhood.overview}</p>
            
            <h3 className="font-bold text-gray-900 mb-2">Highlights</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
              {neighborhood.highlights.map((highlight, index) => (
                <div key={index} className="flex items-start">
                  <div className="bg-blue-100 rounded-full p-2 text-blue-600 mr-3 flex-shrink-0">
                    {highlight.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{highlight.title}</h4>
                    <p className="text-sm text-gray-600">{highlight.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="aspect-video bg-gray-200 rounded-lg mb-4">
              {/* Map placeholder - in a real app, use a proper map component */}
              <div className="w-full h-full flex items-center justify-center text-gray-500">
                Interactive map would be here
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'living' && (
          <div>
            <h3 className="font-bold text-gray-900 mb-2">Living in {neighborhood.name}</h3>
            <p className="text-gray-700 mb-6">{neighborhood.livingInfo}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Pros</h4>
                <ul className="space-y-2">
                  {neighborhood.pros.map((pro, index) => (
                    <li key={index} className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>{pro}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Cons</h4>
                <ul className="space-y-2">
                  {neighborhood.cons.map((con, index) => (
                    <li key={index} className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <span>{con}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'transport' && (
          <div>
            <h3 className="font-bold text-gray-900 mb-4">Transportation Options</h3>
            
            <div className="space-y-4">
              {neighborhood.transportOptions.map((option, index) => (
                <div key={index} className="flex items-start">
                  <div className={`rounded-full w-10 h-10 flex items-center justify-center text-white font-bold mr-3 ${option.color}`}>
                    {option.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{option.name}</h4>
                    <p className="text-gray-600">{option.description}</p>
                    {option.lines && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {option.lines.map((line, lineIndex) => (
                          <span 
                            key={lineIndex} 
                            className={`inline-block px-2 py-1 rounded-full text-xs font-medium text-white ${line.color}`}
                          >
                            {line.name}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {activeTab === 'prices' && (
          <div>
            <h3 className="font-bold text-gray-900 mb-4">Rental Prices in {neighborhood.name}</h3>
            
            <div className="overflow-hidden bg-white shadow sm:rounded-lg mb-6">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Average Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price Range
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {neighborhood.prices.map((price, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {price.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {price.average} TRY
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {price.range} TRY
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            <p className="text-sm text-gray-600">
              *Prices are estimated based on current market trends as of April 2025 and may vary.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default NeighborhoodGuide;