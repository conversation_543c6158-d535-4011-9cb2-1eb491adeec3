// components/roommates/CompatibilityQuiz.jsx
import { useState } from 'react';

const CompatibilityQuiz = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState({
    stayPurpose: '',
    stayDuration: '',
    lifestyle: {
      sleepSchedule: '',
      cleanliness: '',
      socializing: '',
      guests: '',
      noise: ''
    },
    preferences: {
      smoking: '',
      pets: '',
      diet: '',
      sharing: '',
      studyHabits: ''
    },
    interests: [],
    languages: [],
    budget: {
      min: '',
      max: ''
    },
    location: [],
    moveInDate: '',
    additionalInfo: ''
  });

  const handleInputChange = (category, field, value) => {
    if (category) {
      setAnswers({
        ...answers,
        [category]: {
          ...answers[category],
          [field]: value
        }
      });
    } else {
      setAnswers({
        ...answers,
        [field]: value
      });
    }
  };

  const handleArrayChange = (field, value) => {
    if (answers[field].includes(value)) {
      setAnswers({
        ...answers,
        [field]: answers[field].filter(item => item !== value)
      });
    } else {
      setAnswers({
        ...answers,
        [field]: [...answers[field], value]
      });
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(answers);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const steps = [
    // Step 1: Basic Information
    {
      title: "What brings you to Istanbul?",
      content: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Purpose of Stay</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.stayPurpose}
              onChange={(e) => handleInputChange(null, 'stayPurpose', e.target.value)}
            >
              <option value="">Select purpose</option>
              <option value="study">Study (University/College)</option>
              <option value="erasmus">Erasmus Exchange</option>
              <option value="work">Work</option>
              <option value="internship">Internship</option>
              <option value="language">Language Learning</option>
              <option value="digital_nomad">Digital Nomad</option>
              <option value="relocation">Relocation</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Duration of Stay</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.stayDuration}
              onChange={(e) => handleInputChange(null, 'stayDuration', e.target.value)}
            >
              <option value="">Select duration</option>
              <option value="less_than_month">Less than 1 month</option>
              <option value="1_3_months">1-3 months</option>
              <option value="3_6_months">3-6 months</option>
              <option value="6_12_months">6-12 months</option>
              <option value="1_2_years">1-2 years</option>
              <option value="2_4_years">2-4 years</option>
              <option value="more_than_4_years">More than 4 years</option>
              <option value="indefinite">Indefinite</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Move-in Date</label>
            <input
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.moveInDate}
              onChange={(e) => handleInputChange(null, 'moveInDate', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Budget Range (TRY/month)</label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <input
                  type="number"
                  placeholder="Min"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={answers.budget.min}
                  onChange={(e) => handleInputChange('budget', 'min', e.target.value)}
                />
              </div>
              <div>
                <input
                  type="number"
                  placeholder="Max"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={answers.budget.max}
                  onChange={(e) => handleInputChange('budget', 'max', e.target.value)}
                />
              </div>
            </div>
          </div>
        </div>
      )
    },
    // Step 2: Lifestyle Preferences
    {
      title: "What's your lifestyle like?",
      content: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sleep Schedule</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.lifestyle.sleepSchedule}
              onChange={(e) => handleInputChange('lifestyle', 'sleepSchedule', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="early_bird">Early Bird (Early to bed, early to rise)</option>
              <option value="night_owl">Night Owl (Late to bed, late to rise)</option>
              <option value="mixed">Mixed/Flexible</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Cleanliness Level</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.lifestyle.cleanliness}
              onChange={(e) => handleInputChange('lifestyle', 'cleanliness', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="very_neat">Very Neat (Clean daily)</option>
              <option value="neat">Neat (Clean regularly)</option>
              <option value="average">Average (Clean when needed)</option>
              <option value="relaxed">Relaxed (Don't mind some mess)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Socializing Preference</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.lifestyle.socializing}
              onChange={(e) => handleInputChange('lifestyle', 'socializing', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="very_social">Very Social (Love hanging out with roommates)</option>
              <option value="social">Social (Enjoy occasional roommate activities)</option>
              <option value="moderate">Moderate (Friendly but value privacy)</option>
              <option value="private">Private (Prefer keeping to myself)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Guests Policy</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.lifestyle.guests}
              onChange={(e) => handleInputChange('lifestyle', 'guests', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="often">Often (Frequently have friends over)</option>
              <option value="sometimes">Sometimes (Occasionally have guests)</option>
              <option value="rarely">Rarely (Seldom have visitors)</option>
              <option value="never">Never (Prefer no guests)</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Noise Tolerance</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.lifestyle.noise}
              onChange={(e) => handleInputChange('lifestyle', 'noise', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="very_quiet">Very Quiet (Need silence)</option>
              <option value="moderate">Moderate (Some noise is okay)</option>
              <option value="tolerant">Tolerant (Don't mind noise)</option>
            </select>
          </div>
        </div>
      )
    },
    // Step 3: Personal Preferences
    {
      title: "What are your personal preferences?",
      content: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Smoking</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.preferences.smoking}
              onChange={(e) => handleInputChange('preferences', 'smoking', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="smoker">I smoke</option>
              <option value="non_smoker_tolerant">I don't smoke but don't mind smokers</option>
              <option value="non_smoker_intolerant">I don't smoke and prefer non-smokers</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Pets</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.preferences.pets}
              onChange={(e) => handleInputChange('preferences', 'pets', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="have_pets">I have pets</option>
              <option value="no_pets_tolerant">I don't have pets but like them</option>
              <option value="no_pets_intolerant">I don't have pets and prefer no pets</option>
              <option value="allergic">I'm allergic to pets</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Dietary Habits</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.preferences.diet}
              onChange={(e) => handleInputChange('preferences', 'diet', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="no_restrictions">No dietary restrictions</option>
              <option value="vegetarian">Vegetarian</option>
              <option value="vegan">Vegan</option>
              <option value="halal">Halal</option>
              <option value="kosher">Kosher</option>
              <option value="other">Other dietary restrictions</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sharing Common Items</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.preferences.sharing}
              onChange={(e) => handleInputChange('preferences', 'sharing', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="share_all">Happy to share most things</option>
              <option value="share_some">Willing to share some things</option>
              <option value="share_little">Prefer to keep things separate</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Study/Work Habits</label>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={answers.preferences.studyHabits}
              onChange={(e) => handleInputChange('preferences', 'studyHabits', e.target.value)}
            >
              <option value="">Select option</option>
              <option value="home">Study/work mostly at home</option>
              <option value="outside">Study/work mostly outside home</option>
              <option value="mixed">Mix of both</option>
            </select>
          </div>
        </div>
      )
    },
    // Step 4: Interests and Languages
    {
      title: "What are your interests and languages?",
      content: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Interests (Select all that apply)</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                'Sports', 'Fitness', 'Reading', 'Movies', 'Music', 'Art', 'Cooking',
                'Travel', 'Photography', 'Technology', 'Gaming', 'Outdoors',
                'Languages', 'Politics', 'Science', 'Fashion', 'History', 'Philosophy'
              ].map((interest) => (
                <div key={interest} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`interest-${interest}`}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={answers.interests.includes(interest)}
                    onChange={() => handleArrayChange('interests', interest)}
                  />
                  <label htmlFor={`interest-${interest}`} className="ml-2 block text-sm text-gray-700">
                    {interest}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Languages (Select all that apply)</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                'English', 'Turkish', 'Arabic', 'French', 'German', 'Spanish',
                'Italian', 'Russian', 'Chinese', 'Japanese', 'Korean', 'Portuguese'
              ].map((language) => (
                <div key={language} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`language-${language}`}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={answers.languages.includes(language)}
                    onChange={() => handleArrayChange('languages', language)}
                  />
                  <label htmlFor={`language-${language}`} className="ml-2 block text-sm text-gray-700">
                    {language}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    },
    // Step 5: Location Preferences
    {
      title: "Where would you like to live?",
      content: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Preferred Neighborhoods (Select all that apply)</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                'Kadıköy', 'Beşiktaş', 'Şişli', 'Beyoğlu', 'Üsküdar', 'Fatih',
                'Bakırköy', 'Sarıyer', 'Ataşehir', 'Maltepe', 'Levent', 'Moda'
              ].map((neighborhood) => (
                <div key={neighborhood} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`neighborhood-${neighborhood}`}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={answers.location.includes(neighborhood)}
                    onChange={() => handleArrayChange('location', neighborhood)}
                  />
                  <label htmlFor={`neighborhood-${neighborhood}`} className="ml-2 block text-sm text-gray-700">
                    {neighborhood}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Additional Information</label>
            <textarea
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Anything else you'd like potential roommates to know about you?"
              value={answers.additionalInfo}
              onChange={(e) => handleInputChange(null, 'additionalInfo', e.target.value)}
            ></textarea>
          </div>
        </div>
      )
    }
  ];

  const currentStepData = steps[currentStep];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">{currentStepData.title}</h2>
          <span className="text-sm text-gray-500">Step {currentStep + 1} of {steps.length}</span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
          ></div>
        </div>
      </div>

      {currentStepData.content}

      <div className="mt-8 flex justify-between">
        <button
          type="button"
          onClick={handleBack}
          disabled={currentStep === 0}
          className={`px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
            currentStep === 0 ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          Back
        </button>
        <button
          type="button"
          onClick={handleNext}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {currentStep < steps.length - 1 ? 'Next' : 'Find Matches'}
        </button>
      </div>
    </div>
  );
};

export default CompatibilityQuiz;