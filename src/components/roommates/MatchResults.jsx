// components/roommates/MatchResults.jsx
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { dummyRoommates } from '../../utils/dummyData';

const MatchResults = ({ quizAnswers }) => {
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('compatibility'); // 'compatibility' or 'all'

  // Simulate AI matching algorithm
  useEffect(() => {
    // In a real app, this would be an API call to a backend service
    // that implements the AI matching algorithm
    const simulateMatching = () => {
      setLoading(true);
      
      // Simulate API delay
      setTimeout(() => {
        // Create a copy of roommates to manipulate
        const potentialMatches = [...dummyRoommates];
        
        // Calculate compatibility scores (in a real app, this would be done by the backend)
        const matchesWithScores = potentialMatches.map(roommate => {
          // Calculate a compatibility score based on quiz answers
          // This is a simplified version - a real algorithm would be more sophisticated
          let compatibilityScore = 0;
          let matchReasons = [];
          
          // Purpose of stay matching
          if (quizAnswers.stayPurpose) {
            // Simulate matching based on purpose (e.g., students match well with other students)
            if ((quizAnswers.stayPurpose === 'study' || quizAnswers.stayPurpose === 'erasmus') && 
                roommate.occupation.toLowerCase().includes('student')) {
              compatibilityScore += 15;
              matchReasons.push('Similar educational focus');
            } else if (quizAnswers.stayPurpose === 'work' && 
                      !roommate.occupation.toLowerCase().includes('student')) {
              compatibilityScore += 15;
              matchReasons.push('Both working professionals');
            }
          }
          
          // Duration matching
          if (quizAnswers.stayDuration) {
            // Simulate matching based on move-in date and availability
            if ((quizAnswers.stayDuration === 'less_than_month' || quizAnswers.stayDuration === '1_3_months') && 
                roommate.moveInDate.includes('Immediately')) {
              compatibilityScore += 10;
              matchReasons.push('Available when you need');
            }
          }
          
          // Budget compatibility
          if (quizAnswers.budget && quizAnswers.budget.min && quizAnswers.budget.max) {
            const roommateMinBudget = parseInt(roommate.budget.split('-')[0].replace(/[^0-9]/g, ''));
            const roommateMaxBudget = parseInt(roommate.budget.split('-')[1]?.replace(/[^0-9]/g, '') || roommateMinBudget);
            
            const userMinBudget = parseInt(quizAnswers.budget.min);
            const userMaxBudget = parseInt(quizAnswers.budget.max);
            
            // Check if budget ranges overlap
            if (!(userMaxBudget < roommateMinBudget || userMinBudget > roommateMaxBudget)) {
              compatibilityScore += 20;
              matchReasons.push('Compatible budget range');
            }
          }
          
          // Lifestyle compatibility
          if (quizAnswers.lifestyle) {
            // Smoking compatibility
            if (quizAnswers.preferences && quizAnswers.preferences.smoking) {
              if ((quizAnswers.preferences.smoking === 'non_smoker_intolerant' && !roommate.smoking) ||
                  (quizAnswers.preferences.smoking === 'smoker' && roommate.smoking)) {
                compatibilityScore += 10;
                matchReasons.push('Compatible smoking preferences');
              }
            }
            
            // Pets compatibility
            if (quizAnswers.preferences && quizAnswers.preferences.pets) {
              if ((quizAnswers.preferences.pets === 'no_pets_intolerant' && !roommate.pets) ||
                  (quizAnswers.preferences.pets === 'have_pets' && roommate.pets)) {
                compatibilityScore += 10;
                matchReasons.push('Compatible pet preferences');
              }
            }
            
            // Social compatibility (simplified)
            if (quizAnswers.lifestyle.socializing) {
              // Just a simple simulation - in reality would be more complex
              if (Math.random() > 0.5) {
                compatibilityScore += 15;
                matchReasons.push('Similar social preferences');
              }
            }
          }
          
          // Location preferences
          if (quizAnswers.location && quizAnswers.location.length > 0) {
            // Check if roommate's location matches any of the user's preferred locations
            const locationMatches = quizAnswers.location.some(loc => 
              roommate.location.includes(loc)
            );
            
            if (locationMatches) {
              compatibilityScore += 15;
              matchReasons.push('Preferred neighborhood match');
            }
          }
          
          // Language compatibility
          if (quizAnswers.languages && quizAnswers.languages.length > 0 && roommate.languages) {
            const commonLanguages = quizAnswers.languages.filter(lang => 
              roommate.languages.some(l => l.includes(lang))
            );
            
            if (commonLanguages.length > 0) {
              compatibilityScore += 10 * commonLanguages.length;
              matchReasons.push(`Common language${commonLanguages.length > 1 ? 's' : ''}: ${commonLanguages.join(', ')}`);
            }
          }
          
          // Interest compatibility
          if (quizAnswers.interests && quizAnswers.interests.length > 0 && roommate.interests) {
            const commonInterests = quizAnswers.interests.filter(interest => 
              roommate.interests.some(i => i.includes(interest))
            );
            
            if (commonInterests.length > 0) {
              compatibilityScore += 5 * commonInterests.length;
              matchReasons.push(`Shared interest${commonInterests.length > 1 ? 's' : ''}: ${commonInterests.join(', ')}`);
            }
          }
          
          // Add some randomness to make it more realistic
          compatibilityScore += Math.floor(Math.random() * 10);
          
          // Cap at 100%
          compatibilityScore = Math.min(compatibilityScore, 100);
          
          return {
            ...roommate,
            compatibilityScore,
            matchReasons: matchReasons.slice(0, 3) // Limit to top 3 reasons
          };
        });
        
        // Sort by compatibility score
        matchesWithScores.sort((a, b) => b.compatibilityScore - a.compatibilityScore);
        
        setMatches(matchesWithScores);
        setLoading(false);
      }, 1500);
    };
    
    simulateMatching();
  }, [quizAnswers]);

  const getScoreColor = (score) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const filteredMatches = viewMode === 'compatibility' 
    ? matches.filter(match => match.compatibilityScore >= 60)
    : matches;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Your Roommate Matches</h2>
        <p className="text-gray-600">
          Based on your preferences, we've found potential roommates who might be a good fit for you.
        </p>
        
        {/* View Mode Toggle */}
        <div className="mt-4 flex space-x-2">
          <button
            onClick={() => setViewMode('compatibility')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              viewMode === 'compatibility'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Best Matches
          </button>
          <button
            onClick={() => setViewMode('all')}
            className={`px-4 py-2 text-sm font-medium rounded-md ${
              viewMode === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All Potential Matches
          </button>
        </div>
      </div>
      
      {loading ? (
        <div className="py-12 text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Finding your perfect roommate matches...</p>
        </div>
      ) : filteredMatches.length > 0 ? (
        <div className="space-y-6">
          {filteredMatches.map((match) => (
            <div key={match.id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="flex flex-col md:flex-row">
                {/* Profile Image */}
                <div className="md:w-1/4 h-48 md:h-auto relative">
                  <div 
                    className="absolute inset-0 bg-cover bg-center" 
                    style={{ backgroundImage: `url(${match.image})` }}
                  />
                </div>
                
                {/* Match Details */}
                <div className="p-4 md:p-6 md:w-3/4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">{match.name}, {match.age}</h3>
                      <p className="text-gray-600">{match.occupation}</p>
                    </div>
                    <div className="flex items-center">
                      <div className={`text-2xl font-bold ${getScoreColor(match.compatibilityScore)}`}>
                        {match.compatibilityScore}%
                      </div>
                      <span className="ml-1 text-sm text-gray-500">match</span>
                    </div>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Looking in</p>
                      <p className="font-medium">{match.location}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Budget</p>
                      <p className="font-medium">{match.budget}</p>
                    </div>
                  </div>
                  
                  {/* Match Reasons */}
                  {match.matchReasons && match.matchReasons.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-500 mb-2">Why you might get along:</p>
                      <div className="flex flex-wrap gap-2">
                        {match.matchReasons.map((reason, index) => (
                          <span 
                            key={index} 
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {reason}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-6 flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Last active: {match.lastActive}
                    </div>
                    <Link 
                      href={`/roommates/${match.id}`}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      View Profile
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="py-12 text-center">
          <p className="text-gray-600">No matches found. Try adjusting your preferences.</p>
        </div>
      )}
      
      {/* Suggested Listings Section */}
      <div className="mt-12 border-t border-gray-200 pt-8">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Suggested Accommodations</h3>
        <p className="text-gray-600 mb-6">
          Based on your preferences, here are some accommodations that might interest you:
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* This would be populated with actual listings based on preferences */}
          <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
            <div className="h-48 relative">
              <div 
                className="absolute inset-0 bg-cover bg-center" 
                style={{ backgroundImage: "url('https://source.unsplash.com/random/800x600/?apartment,modern')" }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60" />
              <div className="absolute bottom-0 left-0 p-4">
                <h3 className="text-lg font-bold text-white">Modern Apartment in Kadıköy</h3>
                <p className="text-sm text-gray-200">3200 TRY/month</p>
              </div>
            </div>
            <div className="p-4">
              <p className="text-sm text-gray-600 mb-4">Perfect for students, close to universities and public transport.</p>
              <Link 
                href="/listings/1"
                className="text-blue-600 hover:text-blue-800 font-medium text-sm"
              >
                View Details
              </Link>
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
            <div className="h-48 relative">
              <div 
                className="absolute inset-0 bg-cover bg-center" 
                style={{ backgroundImage: "url('https://source.unsplash.com/random/800x600/?apartment,cozy')" }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60" />
              <div className="absolute bottom-0 left-0 p-4">
                <h3 className="text-lg font-bold text-white">Cozy Room in Beşiktaş</h3>
                <p className="text-sm text-gray-200">1800 TRY/month</p>
              </div>
            </div>
            <div className="p-4">
              <p className="text-sm text-gray-600 mb-4">Shared apartment with friendly roommates, all utilities included.</p>
              <Link 
                href="/listings/2"
                className="text-blue-600 hover:text-blue-800 font-medium text-sm"
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
        
        <div className="mt-6 text-center">
          <Link 
            href="/search"
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Browse All Listings
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MatchResults;
