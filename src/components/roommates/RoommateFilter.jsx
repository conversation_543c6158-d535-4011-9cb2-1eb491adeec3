// components/roommates/RoommateFilter.jsx
import { useState } from 'react';
import { useRouter } from 'next/router';

const RoommateFilter = ({ initialFilters, onFilterChange }) => {
  const router = useRouter();
  
  const [filters, setFilters] = useState({
    gender: initialFilters.gender || 'any',
    ageMin: initialFilters.ageMin || '',
    ageMax: initialFilters.ageMax || '',
    budgetMin: initialFilters.budgetMin || '',
    budgetMax: initialFilters.budgetMax || '',
    moveInDate: initialFilters.moveInDate || '',
    smoking: initialFilters.smoking || 'any',
    pets: initialFilters.pets || 'any',
    verified: initialFilters.verified || false,
    ...initialFilters
  });

  const [isExpanded, setIsExpanded] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;
    
    setFilters(prev => ({
      ...prev,
      [name]: newValue
    }));
  };

  const handleApplyFilters = () => {
    // Convert the filters to query params for URL
    const queryParams = { ...router.query };
    
    // Add each filter to the query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'any' && value !== '') {
        queryParams[key] = value;
      } else {
        delete queryParams[key];
      }
    });
    
    // Update the URL with the new filters
    router.push({
      pathname: router.pathname,
      query: queryParams,
    }, undefined, { shallow: true });
    
    // Notify parent component
    onFilterChange(filters);
  };

  const handleResetFilters = () => {
    const resetFilters = {
      gender: 'any',
      ageMin: '',
      ageMax: '',
      budgetMin: '',
      budgetMax: '',
      moveInDate: '',
      smoking: 'any',
      pets: 'any',
      verified: false
    };
    
    setFilters(resetFilters);
    
    // Update URL to remove filter params
    router.push({
      pathname: router.pathname,
      query: { q: router.query.q }, // Preserve search query if any
    }, undefined, { shallow: true });
    
    // Notify parent component
    onFilterChange(resetFilters);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Roommate Filters</h3>
          <button 
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
          >
            {isExpanded ? (
              <>
                <span>Show Less</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </>
            ) : (
              <>
                <span>Show More</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </>
            )}
          </button>
        </div>
      </div>
      
      <div className="p-4 space-y-6">
        {/* Gender */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
          <select
            name="gender"
            value={filters.gender}
            onChange={handleInputChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          >
            <option value="any">Any Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        {/* Age Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Age Range</label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <input
                type="number"
                name="ageMin"
                placeholder="Min Age"
                value={filters.ageMin}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                min="18"
                max="99"
              />
            </div>
            <div>
              <input
                type="number"
                name="ageMax"
                placeholder="Max Age"
                value={filters.ageMax}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                min="18"
                max="99"
              />
            </div>
          </div>
        </div>
        
        {/* Budget Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Budget Range (TRY)</label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <input
                type="number"
                name="budgetMin"
                placeholder="Min"
                value={filters.budgetMin}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
            <div>
              <input
                type="number"
                name="budgetMax"
                placeholder="Max"
                value={filters.budgetMax}
                onChange={handleInputChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        </div>
        
        {/* Move-in Date */}
        <div>
          <label htmlFor="moveInDate" className="block text-sm font-medium text-gray-700 mb-2">
            Move-in Date (earliest)
          </label>
          <input
            type="date"
            id="moveInDate"
            name="moveInDate"
            value={filters.moveInDate}
            onChange={handleInputChange}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        {/* Verification Status - Checkbox */}
        <div className="flex items-center">
          <input
            id="verified"
            name="verified"
            type="checkbox"
            checked={filters.verified}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="verified" className="ml-2 block text-sm text-gray-900">
            Verified Profiles Only
          </label>
        </div>
        
        {/* Additional filters that show when expanded */}
        {isExpanded && (
          <div className="space-y-6 pt-4 border-t border-gray-200">
            {/* Smoking Preference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Smoking</label>
              <select
                name="smoking"
                value={filters.smoking}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="any">Any</option>
                <option value="yes">Smoker</option>
                <option value="no">Non-smoker</option>
              </select>
            </div>
            
            {/* Pet Preference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Pets</label>
              <select
                name="pets"
                value={filters.pets}
                onChange={handleInputChange}
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="any">Any</option>
                <option value="yes">Has Pets</option>
                <option value="no">No Pets</option>
              </select>
            </div>
            
            {/* Can add more filters here */}
          </div>
        )}
        
        {/* Action Buttons */}
        <div className="flex justify-between pt-4">
          <button
            onClick={handleResetFilters}
            className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Reset All
          </button>
          <button
            onClick={handleApplyFilters}
            className="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default RoommateFilter;