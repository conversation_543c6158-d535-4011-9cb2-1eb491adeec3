// components/roommates/MatchingInterface.jsx
import { useState } from 'react';
import CompatibilityQuiz from './CompatibilityQuiz';
import MatchResults from './MatchResults';

const MatchingInterface = () => {
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [quizAnswers, setQuizAnswers] = useState(null);
  const [showIntro, setShowIntro] = useState(true);

  const handleQuizComplete = (answers) => {
    setQuizAnswers(answers);
    setQuizCompleted(true);
  };

  const handleStartQuiz = () => {
    setShowIntro(false);
  };

  const handleReset = () => {
    setQuizCompleted(false);
    setQuizAnswers(null);
    setShowIntro(true);
  };

  return (
    <div className="max-w-4xl mx-auto">
      {showIntro ? (
        <div className="bg-white rounded-lg shadow-md p-8 text-center">
          <div className="mb-6">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">AI-Powered Roommate Matching</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our advanced matching system helps you find compatible roommates based on lifestyle, preferences, and personality traits. Take our comprehensive quiz to get personalized matches.
            </p>
          </div>

          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">How It Works</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Complete the Quiz</h4>
                <p className="text-sm text-gray-600">Answer questions about your lifestyle, preferences, and what you're looking for</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">2</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Get Matched</h4>
                <p className="text-sm text-gray-600">Our AI analyzes your responses to find compatible roommates</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">3</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-1">Connect</h4>
                <p className="text-sm text-gray-600">Reach out to your matches and find your perfect roommate</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Perfect For:</h3>
            <div className="flex flex-wrap justify-center gap-3">
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                Erasmus Students
              </span>
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                International Students
              </span>
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Young Professionals
              </span>
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                Digital Nomads
              </span>
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 text-red-800">
                Short-term Stays
              </span>
              <span className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                Long-term Residents
              </span>
            </div>
          </div>

          <button
            onClick={handleStartQuiz}
            className="mt-8 inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Start Roommate Matching Quiz
          </button>
        </div>
      ) : quizCompleted ? (
        <div>
          <MatchResults quizAnswers={quizAnswers} />
          <div className="mt-6 text-center">
            <button
              onClick={handleReset}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Start Over
            </button>
          </div>
        </div>
      ) : (
        <CompatibilityQuiz onComplete={handleQuizComplete} />
      )}
    </div>
  );
};

export default MatchingInterface;
