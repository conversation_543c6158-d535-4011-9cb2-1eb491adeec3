// components/listings/VirtualTour.jsx
import { useState } from 'react';

const VirtualTour = ({ panoramaImages = [] }) => {
  const [currentImage, setCurrentImage] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [offsetX, setOffsetX] = useState(0);
  
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartX(e.clientX - offsetX);
  };
  
  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const newOffset = e.clientX - startX;
    setOffsetX(newOffset % 360);
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleImageChange = (index) => {
    setCurrentImage(index);
    setOffsetX(0);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-bold text-gray-900">Virtual Tour</h3>
      </div>
      
      {/* 360 Viewer (simplified) */}
      <div 
        className="relative h-96 bg-black cursor-grab"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {panoramaImages.length > 0 ? (
          <div 
            className="absolute inset-0 bg-cover bg-center"
            style={{ 
              backgroundImage: `url(${panoramaImages[currentImage]})`,
              transform: `translateX(${offsetX}px)` 
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-white">360° tour not available for this property</p>
          </div>
        )}
        
        {/* Navigation overlay */}
        <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black to-transparent p-4">
          <div className="flex justify-center space-x-2">
            {panoramaImages.map((_, index) => (
              <button
                key={index}
                onClick={() => handleImageChange(index)}
                className={`w-3 h-3 rounded-full ${currentImage === index ? 'bg-blue-500' : 'bg-white bg-opacity-50'}`}
              />
            ))}
          </div>
          <p className="text-white text-center text-sm mt-2">
            Click and drag to look around
          </p>
        </div>
      </div>
    </div>
  );
};

export default VirtualTour;