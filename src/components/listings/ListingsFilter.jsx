import React, { useState } from 'react';

const ListingsFilter = ({ onFilter, initialFilters = {} }) => {
  const [filters, setFilters] = useState({
    propertyType: initialFilters.propertyType || '',
    roomType: initialFilters.roomType || '',
    city: initialFilters.city || '',
    country: initialFilters.country || '',
    minPrice: initialFilters.minPrice || '',
    maxPrice: initialFilters.maxPrice || '',
    bedrooms: initialFilters.bedrooms || '',
    bathrooms: initialFilters.bathrooms || '',
    ...initialFilters
  });

  const [showMore, setShowMore] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onFilter(filters);
  };

  const handleReset = () => {
    setFilters({
      propertyType: '',
      roomType: '',
      city: '',
      country: '',
      minPrice: '',
      maxPrice: '',
      bedrooms: '',
      bathrooms: '',
      wifi: false,
      airConditioning: false,
      heating: false,
      kitchen: false,
      tv: false,
      parking: false,
      elevator: false,
      furnished: false,
      petsAllowed: false,
      smokingAllowed: false,
    });
    onFilter({});
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter Listings</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          {/* Property Type */}
          <div>
            <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700 mb-1">
              Property Type
            </label>
            <select
              id="propertyType"
              name="propertyType"
              value={filters.propertyType}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Any Type</option>
              <option value="apartment">Apartment</option>
              <option value="house">House</option>
              <option value="room">Room</option>
              <option value="studio">Studio</option>
              <option value="dormitory">Dormitory</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          {/* Room Type */}
          <div>
            <label htmlFor="roomType" className="block text-sm font-medium text-gray-700 mb-1">
              Room Type
            </label>
            <select
              id="roomType"
              name="roomType"
              value={filters.roomType}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Any Room Type</option>
              <option value="entire_place">Entire Place</option>
              <option value="private_room">Private Room</option>
              <option value="shared_room">Shared Room</option>
            </select>
          </div>
          
          {/* City */}
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              type="text"
              id="city"
              name="city"
              value={filters.city}
              onChange={handleChange}
              placeholder="Any City"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          {/* Country */}
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <input
              type="text"
              id="country"
              name="country"
              value={filters.country}
              onChange={handleChange}
              placeholder="Any Country"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          {/* Price Range */}
          <div>
            <label htmlFor="minPrice" className="block text-sm font-medium text-gray-700 mb-1">
              Price Range
            </label>
            <div className="flex space-x-2">
              <input
                type="number"
                id="minPrice"
                name="minPrice"
                value={filters.minPrice}
                onChange={handleChange}
                placeholder="Min"
                min="0"
                className="w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <input
                type="number"
                id="maxPrice"
                name="maxPrice"
                value={filters.maxPrice}
                onChange={handleChange}
                placeholder="Max"
                min="0"
                className="w-1/2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          {/* Bedrooms */}
          <div>
            <label htmlFor="bedrooms" className="block text-sm font-medium text-gray-700 mb-1">
              Bedrooms
            </label>
            <select
              id="bedrooms"
              name="bedrooms"
              value={filters.bedrooms}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Any</option>
              <option value="1">1+</option>
              <option value="2">2+</option>
              <option value="3">3+</option>
              <option value="4">4+</option>
              <option value="5">5+</option>
            </select>
          </div>
        </div>
        
        {/* More Filters */}
        {showMore && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <h3 className="text-md font-medium text-gray-900 mb-3">Amenities</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              {/* WiFi */}
              <div className="flex items-center">
                <input
                  id="wifi"
                  name="wifi"
                  type="checkbox"
                  checked={filters.wifi || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="wifi" className="ml-2 text-sm text-gray-700">
                  WiFi
                </label>
              </div>
              
              {/* Air Conditioning */}
              <div className="flex items-center">
                <input
                  id="airConditioning"
                  name="airConditioning"
                  type="checkbox"
                  checked={filters.airConditioning || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="airConditioning" className="ml-2 text-sm text-gray-700">
                  Air Conditioning
                </label>
              </div>
              
              {/* Heating */}
              <div className="flex items-center">
                <input
                  id="heating"
                  name="heating"
                  type="checkbox"
                  checked={filters.heating || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="heating" className="ml-2 text-sm text-gray-700">
                  Heating
                </label>
              </div>
              
              {/* Kitchen */}
              <div className="flex items-center">
                <input
                  id="kitchen"
                  name="kitchen"
                  type="checkbox"
                  checked={filters.kitchen || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="kitchen" className="ml-2 text-sm text-gray-700">
                  Kitchen
                </label>
              </div>
              
              {/* TV */}
              <div className="flex items-center">
                <input
                  id="tv"
                  name="tv"
                  type="checkbox"
                  checked={filters.tv || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="tv" className="ml-2 text-sm text-gray-700">
                  TV
                </label>
              </div>
              
              {/* Parking */}
              <div className="flex items-center">
                <input
                  id="parking"
                  name="parking"
                  type="checkbox"
                  checked={filters.parking || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="parking" className="ml-2 text-sm text-gray-700">
                  Parking
                </label>
              </div>
              
              {/* Elevator */}
              <div className="flex items-center">
                <input
                  id="elevator"
                  name="elevator"
                  type="checkbox"
                  checked={filters.elevator || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="elevator" className="ml-2 text-sm text-gray-700">
                  Elevator
                </label>
              </div>
              
              {/* Furnished */}
              <div className="flex items-center">
                <input
                  id="furnished"
                  name="furnished"
                  type="checkbox"
                  checked={filters.furnished || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="furnished" className="ml-2 text-sm text-gray-700">
                  Furnished
                </label>
              </div>
              
              {/* Pets Allowed */}
              <div className="flex items-center">
                <input
                  id="petsAllowed"
                  name="petsAllowed"
                  type="checkbox"
                  checked={filters.petsAllowed || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="petsAllowed" className="ml-2 text-sm text-gray-700">
                  Pets Allowed
                </label>
              </div>
              
              {/* Smoking Allowed */}
              <div className="flex items-center">
                <input
                  id="smokingAllowed"
                  name="smokingAllowed"
                  type="checkbox"
                  checked={filters.smokingAllowed || false}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="smokingAllowed" className="ml-2 text-sm text-gray-700">
                  Smoking Allowed
                </label>
              </div>
            </div>
          </div>
        )}
        
        <div className="flex flex-wrap items-center justify-between mt-4">
          <button
            type="button"
            onClick={() => setShowMore(!showMore)}
            className="text-sm text-blue-600 hover:text-blue-800 mb-2 sm:mb-0"
          >
            {showMore ? 'Show Less Filters' : 'Show More Filters'}
          </button>
          
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleReset}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ListingsFilter;
