// components/listings/AvailabilityCalendar.jsx
import { useState } from 'react';

const AvailabilityCalendar = ({ availableDates = [], onDateSelect }) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  
  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const daysInMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      0
    ).getDate();
    
    const firstDayOfMonth = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    ).getDay();
    
    // Create array of day objects
    const days = Array.from({ length: daysInMonth }, (_, i) => {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), i + 1);
      
      // Check if date is available
      const dateString = date.toISOString().split('T')[0];
      const isAvailable = availableDates.includes(dateString);
      
      return { date, isAvailable };
    });
    
    // Add empty cells for days before the first of the month
    const emptyCells = Array(firstDayOfMonth).fill(null);
    
    return [...emptyCells, ...days];
  };
  
  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };
  
  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };
  
  const handleDateClick = (day) => {
    if (!day || !day.isAvailable) return;
    
    setSelectedDate(day.date);
    if (onDateSelect) {
      onDateSelect(day.date);
    }
  };
  
  const days = generateCalendarDays();
  const monthName = currentMonth.toLocaleString('default', { month: 'long' });
  const year = currentMonth.getFullYear();

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 bg-blue-600 text-white">
        <div className="flex justify-between items-center">
          <button onClick={prevMonth} className="p-1 rounded-full hover:bg-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </button>
          <h3 className="text-lg font-bold">{monthName} {year}</h3>
          <button onClick={nextMonth} className="p-1 rounded-full hover:bg-blue-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
            <div key={day} className="text-center text-gray-500 text-sm py-2">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-1">
          {days.map((day, index) => (
            <div
              key={index}
              onClick={() => handleDateClick(day)}
              className={`
                h-10 flex items-center justify-center text-sm rounded-full
                ${!day ? 'text-gray-300' : 
                  day.isAvailable 
                    ? 'cursor-pointer hover:bg-blue-50 text-gray-700' 
                    : 'text-gray-400 line-through bg-gray-50'
                }
                ${selectedDate && day && day.date.toDateString() === selectedDate.toDateString() 
                  ? 'bg-blue-600 text-white hover:bg-blue-600' 
                  : ''}
              `}
            >
              {day ? day.date.getDate() : ''}
            </div>
          ))}
        </div>
        
        <div className="mt-4 flex items-center">
          <div className="flex items-center mr-4">
            <div className="w-3 h-3 rounded-full bg-blue-600 mr-1"></div>
            <span className="text-xs text-gray-600">Available</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-gray-200 mr-1"></div>
            <span className="text-xs text-gray-600">Unavailable</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvailabilityCalendar;