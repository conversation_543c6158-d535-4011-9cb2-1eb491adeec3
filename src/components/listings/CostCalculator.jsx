// components/listings/CostCalculator.jsx
import { useState, useEffect } from 'react';

const CostCalculator = ({ baseRent, currency = 'TRY', period = 'month' }) => {
  const [totalCost, setTotalCost] = useState(baseRent);
  const [utilities, setUtilities] = useState({
    electricity: true,
    water: true,
    internet: true,
    heating: true,
    cleaning: false
  });
  
  const utilityCosts = {
    electricity: 300,
    water: 150,
    internet: 200,
    heating: 400,
    cleaning: 250
  };
  
  useEffect(() => {
    let total = baseRent;
    Object.keys(utilities).forEach(key => {
      if (utilities[key]) {
        total += utilityCosts[key];
      }
    });
    setTotalCost(total);
  }, [utilities, baseRent]);
  
  const handleUtilityToggle = (utility) => {
    setUtilities(prev => ({
      ...prev,
      [utility]: !prev[utility]
    }));
  };
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-bold text-gray-900 mb-4">
        Estimated Monthly Costs
      </h3>
      
      <div className="space-y-4">
        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
          <span className="font-medium">Base Rent</span>
          <span>{baseRent.toLocaleString()} {currency}</span>
        </div>
        
        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-700">Utilities & Services</p>
          
          {Object.keys(utilities).map(utility => (
            <div key={utility} className="flex items-center justify-between">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={utilities[utility]}
                  onChange={() => handleUtilityToggle(utility)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-gray-700 capitalize">{utility}</span>
              </label>
              <span className="text-gray-600">{utilityCosts[utility].toLocaleString()} {currency}</span>
            </div>
          ))}
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <span className="font-bold">Total Estimated Cost</span>
            <span className="text-xl font-bold text-blue-600">
              {totalCost.toLocaleString()} {currency}/{period}
            </span>
          </div>
          <p className="mt-2 text-xs text-gray-500">
            *These are estimated costs and may vary based on usage and provider rates.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CostCalculator;