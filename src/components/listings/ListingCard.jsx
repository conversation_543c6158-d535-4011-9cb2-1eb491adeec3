import React from 'react';
import Link from 'next/link';
import { useAuth } from '../../utils/authContext';
import { toggleSaveListing } from '../../utils/listingService';

const ListingCard = ({ listing, onSaveToggle, showSaveButton = true }) => {
  const { user } = useAuth();
  const [isSaved, setIsSaved] = React.useState(
    listing.savedBy?.includes(user?._id) || false
  );
  const [saving, setSaving] = React.useState(false);

  // Format price with currency symbol
  const formatPrice = (price) => {
    const currencySymbols = {
      TRY: '₺',
      USD: '$',
      EUR: '€',
      GBP: '£',
    };

    const symbol = currencySymbols[price.currency] || '';
    return `${symbol}${price.amount.toLocaleString()}`;
  };

  // Format payment period
  const formatPeriod = (period) => {
    switch (period) {
      case 'daily':
        return '/day';
      case 'weekly':
        return '/week';
      case 'monthly':
        return '/month';
      case 'yearly':
        return '/year';
      default:
        return '';
    }
  };

  // Handle save/unsave
  const handleSaveToggle = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!user) {
      // Redirect to login if not logged in
      window.location.href = '/auth/login?redirect=' + encodeURIComponent(window.location.pathname);
      return;
    }

    setSaving(true);
    try {
      const action = isSaved ? 'unsave' : 'save';
      const result = await toggleSaveListing(listing._id, action);
      
      if (result.success) {
        setIsSaved(!isSaved);
        if (onSaveToggle) {
          onSaveToggle(listing._id, !isSaved);
        }
      }
    } catch (error) {
      console.error('Error toggling save:', error);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
      <Link href={`/listings/${listing._id}`}>
        <div className="relative">
          {/* Main Image */}
          <div className="h-48 overflow-hidden">
            {listing.images && listing.images.length > 0 ? (
              <img
                src={listing.mainImage || listing.images[0].url}
                alt={listing.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 22V12h6v10" />
                </svg>
              </div>
            )}
          </div>

          {/* Property Type Badge */}
          <div className="absolute top-2 left-2">
            <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full uppercase">
              {listing.propertyType}
            </span>
          </div>

          {/* Save Button */}
          {showSaveButton && (
            <button
              onClick={handleSaveToggle}
              disabled={saving}
              className={`absolute top-2 right-2 p-2 rounded-full ${
                isSaved ? 'bg-red-500 text-white' : 'bg-white text-gray-700'
              } shadow-md transition-colors duration-300 hover:bg-opacity-90`}
              aria-label={isSaved ? 'Unsave listing' : 'Save listing'}
            >
              {saving ? (
                <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill={isSaved ? 'currentColor' : 'none'} viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              )}
            </button>
          )}
        </div>

        <div className="p-4">
          {/* Price */}
          <div className="flex justify-between items-start mb-2">
            <div>
              <span className="text-xl font-bold text-gray-900">
                {formatPrice(listing.price)}
              </span>
              <span className="text-sm text-gray-600">
                {formatPeriod(listing.price.paymentPeriod)}
              </span>
            </div>
            
            {/* Status Badge (if owner is viewing) */}
            {listing.owner._id === user?._id && (
              <span className={`text-xs px-2 py-1 rounded-full ${
                listing.status === 'published' ? 'bg-green-100 text-green-800' :
                listing.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                listing.status === 'rented' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {listing.status.charAt(0).toUpperCase() + listing.status.slice(1)}
              </span>
            )}
          </div>

          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-1">
            {listing.title}
          </h3>

          {/* Location */}
          <p className="text-sm text-gray-600 mb-2 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            {listing.address.city}, {listing.address.country}
          </p>

          {/* Features */}
          <div className="flex items-center text-sm text-gray-700 mb-3">
            {listing.rooms.bedrooms > 0 && (
              <div className="flex items-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {listing.rooms.bedrooms} {listing.rooms.bedrooms === 1 ? 'Bedroom' : 'Bedrooms'}
              </div>
            )}
            
            {listing.rooms.bathrooms > 0 && (
              <div className="flex items-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {listing.rooms.bathrooms} {listing.rooms.bathrooms === 1 ? 'Bathroom' : 'Bathrooms'}
              </div>
            )}
            
            {listing.size.value > 0 && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                </svg>
                {listing.size.value} {listing.size.unit}
              </div>
            )}
          </div>

          {/* Available From */}
          <div className="text-xs text-gray-500">
            Available from: {new Date(listing.availability.availableFrom).toLocaleDateString()}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ListingCard;
