// components/home/<USER>
import { useState } from 'react';
import { useRouter } from 'next/router';

const Hero = () => {
  const router = useRouter();
  const [location, setLocation] = useState('');
  const [propertyType, setPropertyType] = useState('all');
  const [priceRange, setPriceRange] = useState('all');

  const handleSearch = (e) => {
    e.preventDefault();

    // Build query parameters
    const query = {};

    // Add location search
    if (location.trim()) {
      query.q = location.trim();
      query.city = location.trim();
    }

    // Add property type filter
    if (propertyType && propertyType !== 'all') {
      query.propertyType = propertyType;
    }

    // Add price range filter
    if (priceRange && priceRange !== 'all') {
      const [min, max] = priceRange.split('-');
      if (min) query.priceMin = min;
      if (max && max !== 'plus') query.priceMax = max;
    }

    router.push({
      pathname: '/search',
      query
    });
  };

  return (
    <div className="relative bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 overflow-hidden min-h-screen">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Main Content */}
      <div className="relative px-4 py-16 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        {/* Hero Text */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-7xl font-bold text-blue-900 mb-6 leading-tight">
            FIND YOUR LIVING SPACE,<br />
            <span className="text-blue-700">MEET YOUR HOMIE</span>
          </h1>
        </div>

        {/* Search Form */}
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSearch} className="bg-white rounded-2xl shadow-2xl overflow-hidden p-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Property Type */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Property Type</label>
                <div className="relative">
                  <select
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600 appearance-none"
                    value={propertyType}
                    onChange={(e) => setPropertyType(e.target.value)}
                  >
                    <option value="all">All Properties</option>
                    <option value="apartment">Apartment</option>
                    <option value="house">House</option>
                    <option value="room">Room</option>
                    <option value="studio">Studio</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Location */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Location</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Enter Location"
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Price Range */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Price Range</label>
                <div className="relative">
                  <select
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600 appearance-none"
                    value={priceRange}
                    onChange={(e) => setPriceRange(e.target.value)}
                  >
                    <option value="all">Any Price</option>
                    <option value="0-10000">Under PKR 10,000</option>
                    <option value="10000-20000">PKR 10,000 - 20,000</option>
                    <option value="20000-30000">PKR 20,000 - 30,000</option>
                    <option value="30000-50000">PKR 30,000 - 50,000</option>
                    <option value="50000-plus">PKR 50,000+</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Search Button */}
              <div className="flex items-end">
                <button
                  type="submit"
                  className="w-full bg-blue-800 hover:bg-blue-900 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Search
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Property Image */}
        <div className="mt-16 max-w-6xl mx-auto">
          <div className="relative rounded-3xl overflow-hidden shadow-2xl">
            <img
              src="/images/modern-apartment.jpg"
              alt="Modern apartment building"
              className="w-full h-96 object-cover"
              onError={(e) => {
                e.target.src = "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=1470&q=80";
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;