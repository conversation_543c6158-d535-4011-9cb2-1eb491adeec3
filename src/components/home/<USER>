// components/home/<USER>
import Link from 'next/link';

const neighborhoods = [
  {
    id: 'kadikoy',
    name: 'Kadıköy',
    description: 'Vibrant streets, cafes, and cultural scene on the Asian side',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,kadikoy',
    listingCount: 124,
    averagePrice: 2800,
    popular: true
  },
  {
    id: 'besiktas',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    description: 'Lively district with universities, nightlife, and Bosphorus views',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,besiktas',
    listingCount: 97,
    averagePrice: 3200,
    popular: true
  },
  {
    id: 'beyoglu',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Historic center with Istiklal Street, Galata Tower, and trendy areas',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,beyoglu',
    listingCount: 142,
    averagePrice: 3500,
    popular: true
  },
  {
    id: 'sis<PERSON>',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Business hub with modern apartments and shopping centers',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,sisli',
    listingCount: 78,
    averagePrice: 3100,
    popular: true
  },
  {
    id: 'uskudar',
    name: 'Üsküdar',
    description: 'Traditional area on the Asian side with beautiful mosques and markets',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,uskudar',
    listingCount: 63,
    averagePrice: 2600,
    popular: false
  },
  {
    id: 'fatih',
    name: 'Fatih',
    description: 'Historical peninsula with Sultanahmet, Hagia Sophia, and Grand Bazaar',
    image: 'https://source.unsplash.com/random/800x600/?istanbul,fatih',
    listingCount: 55,
    averagePrice: 2900,
    popular: false
  }
];

const NeighborhoodSpotlight = () => {
  // Filter to show only popular neighborhoods (up to 4)
  const popularNeighborhoods = neighborhoods.filter(n => n.popular).slice(0, 4);
  
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Popular Istanbul Neighborhoods
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
            Discover the most sought-after areas to live in Istanbul
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {popularNeighborhoods.map((neighborhood) => (
            <Link 
              href={`/neighborhoods/${neighborhood.id}`}
              key={neighborhood.id}
              className="group"
            >
              <div className="relative overflow-hidden rounded-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300 h-full flex flex-col">
                {/* Image */}
                <div className="relative h-56 w-full overflow-hidden">
                  <div 
                    className="absolute inset-0 bg-cover bg-center transform group-hover:scale-110 transition-transform duration-500 ease-in-out"
                    style={{ backgroundImage: `url(${neighborhood.image})` }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-70" />
                  <div className="absolute bottom-0 left-0 p-4 z-10">
                    <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-4 flex-grow">
                  <p className="text-gray-600 mb-4">{neighborhood.description}</p>
                  <div className="flex justify-between text-sm">
                    <div className="flex items-center text-blue-600">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                      </svg>
                      <span>{neighborhood.listingCount} Listings</span>
                    </div>
                    <div className="flex items-center text-gray-700">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                      </svg>
                      <span>Avg {neighborhood.averagePrice} TRY/mo</span>
                    </div>
                  </div>
                </div>
                
                {/* Footer */}
                <div className="p-4 border-t border-gray-100">
                  <div className="flex justify-end items-center text-blue-600 font-medium group-hover:translate-x-1 transition-transform duration-300">
                    Explore {neighborhood.name}
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Link 
            href="/neighborhoods" 
            className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            View All Neighborhoods
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default NeighborhoodSpotlight;