// components/home/<USER>
import { useState, useRef } from 'react';
import Link from 'next/link';

// Dummy data for featured listings
const dummyListings = [
  {
    id: '1',
    title: 'Modern Shared Apartment in Kadıköy',
    type: 'Shared Apartment',
    location: 'Kadıköy, Istanbul',
    price: 3200,
    currency: 'TRY',
    period: 'month',
    beds: 3,
    baths: 1,
    area: 110,
    image: 'https://source.unsplash.com/random/800x600/?apartment,modern',
    verified: true,
    featured: true,
    available: 'Immediately',
    furnished: true
  },
  {
    id: '2',
    title: 'Cozy Private Room in Beşiktaş',
    type: 'Private Room',
    location: 'Beşiktaş, Istanbul',
    price: 1800,
    currency: 'TRY',
    period: 'month',
    beds: 1,
    baths: 1,
    area: 25,
    image: 'https://source.unsplash.com/random/800x600/?bedroom,cozy',
    verified: true,
    featured: true,
    available: 'May 1, 2025',
    furnished: true
  },
  {
    id: '3',
    title: 'Stylish Apartment with Bosphorus View',
    type: 'Short Stay',
    location: 'Üsküdar, Istanbul',
    price: 250,
    currency: 'TRY',
    period: 'night',
    beds: 2,
    baths: 1,
    area: 80,
    image: 'https://source.unsplash.com/random/800x600/?apartment,view',
    verified: true,
    featured: true,
    available: 'Immediately',
    furnished: true
  },
  {
    id: '4',
    title: 'Student-Friendly Apartment near Universities',
    type: 'Shared Apartment',
    location: 'Şişli, Istanbul',
    price: 2500,
    currency: 'TRY',
    period: 'month',
    beds: 2,
    baths: 1,
    area: 70,
    image: 'https://source.unsplash.com/random/800x600/?student,apartment',
    verified: false,
    featured: true,
    available: 'June 15, 2025',
    furnished: false
  },
  {
    id: '5',
    title: 'Luxury Penthouse for Short Stays',
    type: 'Short Stay',
    location: 'Beyoğlu, Istanbul',
    price: 450,
    currency: 'TRY',
    period: 'night',
    beds: 3,
    baths: 2,
    area: 150,
    image: 'https://source.unsplash.com/random/800x600/?penthouse,luxury',
    verified: true,
    featured: true,
    available: 'Immediately',
    furnished: true
  },
  {
    id: '6',
    title: 'Comfortable Room in Historic District',
    type: 'Private Room',
    location: 'Fatih, Istanbul',
    price: 1500,
    currency: 'TRY',
    period: 'month',
    beds: 1,
    baths: 1,
    area: 20,
    image: 'https://source.unsplash.com/random/800x600/?historic,room',
    verified: true,
    featured: true,
    available: 'May 10, 2025',
    furnished: true
  }
];

const FeaturedListings = () => {
  const carouselRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const scrollToNext = () => {
    if (carouselRef.current) {
      const newIndex = Math.min(activeIndex + 1, dummyListings.length - 3);
      setActiveIndex(newIndex);
      const cardWidth = carouselRef.current.offsetWidth / 3;
      carouselRef.current.scrollTo({
        left: cardWidth * newIndex,
        behavior: 'smooth'
      });
    }
  };

  const scrollToPrev = () => {
    if (carouselRef.current) {
      const newIndex = Math.max(activeIndex - 1, 0);
      setActiveIndex(newIndex);
      const cardWidth = carouselRef.current.offsetWidth / 3;
      carouselRef.current.scrollTo({
        left: cardWidth * newIndex,
        behavior: 'smooth'
      });
    }
  };

  return (
    <section className="py-20 gradient-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h2 className="text-4xl font-bold text-blue-900 mb-4">Featured Properties</h2>
            <p className="text-xl text-gray-600">Handpicked premium listings just for you</p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={scrollToPrev}
              disabled={activeIndex === 0}
              className={`p-3 rounded-full transition-all duration-200 ${activeIndex === 0 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-white text-blue-800 hover:bg-blue-50 shadow-md hover:shadow-lg'}`}
              aria-label="Previous"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={scrollToNext}
              disabled={activeIndex >= dummyListings.length - 3}
              className={`p-3 rounded-full transition-all duration-200 ${activeIndex >= dummyListings.length - 3 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-white text-blue-800 hover:bg-blue-50 shadow-md hover:shadow-lg'}`}
              aria-label="Next"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Listings Carousel */}
        <div 
          ref={carouselRef}
          className="flex space-x-6 overflow-x-auto pb-4 scrollbar-hide snap-x snap-mandatory"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {dummyListings.map((listing) => (
            <div
              key={listing.id}
              className="flex-none w-full sm:w-1/2 lg:w-1/3 snap-start"
            >
              <div className="card overflow-hidden h-full hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                <div className="relative h-56 w-full">
                  <div
                    className="absolute inset-0 bg-cover bg-center"
                    style={{ backgroundImage: `url(${listing.image})` }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  {listing.verified && (
                    <div className="absolute top-3 left-3 bg-blue-800 text-white text-xs font-bold px-3 py-1.5 rounded-full flex items-center shadow-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Verified
                    </div>
                  )}
                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-blue-800 text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg">
                    {listing.type}
                  </div>
                  <button className="absolute bottom-3 right-3 bg-white/90 backdrop-blur-sm p-2.5 rounded-full text-gray-600 hover:text-red-500 hover:bg-white transition-all duration-300 shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-gray-900 line-clamp-2 mb-2">{listing.title}</h3>
                      <p className="text-sm text-gray-600 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {listing.location}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <p className="text-xl font-bold text-blue-800">
                        {listing.price.toLocaleString()} {listing.currency}
                      </p>
                      <p className="text-sm text-gray-500">per {listing.period}</p>
                    </div>
                  </div>

                  <div className="flex justify-between text-sm text-gray-600 mb-4 py-3 bg-gray-50 rounded-lg px-3">
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                      </svg>
                      {listing.beds} Bed{listing.beds !== 1 ? 's' : ''}
                    </div>
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                      </svg>
                      {listing.baths} Bath{listing.baths !== 1 ? 's' : ''}
                    </div>
                    <div className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                      </svg>
                      {listing.area} m²
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-4 text-sm">
                    <div className="flex items-center text-blue-600 font-medium">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {listing.available}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <div className={`w-2 h-2 rounded-full mr-2 ${listing.furnished ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      {listing.furnished ? 'Furnished' : 'Unfurnished'}
                    </div>
                  </div>

                  <Link href={`/listings/${listing.id}`} className="btn-primary w-full text-center py-3 text-sm">
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link href="/search" className="btn-secondary inline-flex items-center">
            View All Properties
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedListings;