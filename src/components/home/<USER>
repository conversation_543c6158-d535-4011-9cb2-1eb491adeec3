// components/home/<USER>
import { useState, useRef } from 'react';

const testimonials = [
  {
    id: 1,
    content: "I found my perfect shared apartment in Kadıköy within just a week of searching on Istanbul Rooms. The filters made it so easy to find exactly what I was looking for, and the secure messaging system made me feel comfortable reaching out to potential roommates.",
    author: {
      name: "<PERSON>",
      role: "Digital Nomad from UK",
      image: "https://source.unsplash.com/random/100x100/?portrait,woman,1"
    }
  },
  {
    id: 2,
    content: "As an international student, finding affordable housing near my university was my biggest concern. Istanbul Rooms connected me with a great roommate and a cozy room in Beşiktaş. The neighborhood guides were super helpful too!",
    author: {
      name: "<PERSON>",
      role: "Student at Boğaziçi University",
      image: "https://source.unsplash.com/random/100x100/?portrait,man,1"
    }
  },
  {
    id: 3,
    content: "The verification system on Istanbul Rooms gave me peace of mind when I was looking for a short-term stay. I could trust the listings were legitimate, and the host reviews helped me choose a great place in Beyoğlu.",
    author: {
      name: "<PERSON>",
      role: "Exchange Student from Canada",
      image: "https://source.unsplash.com/random/100x100/?portrait,woman,2"
    }
  },
  {
    id: 4,
    content: "Listing my spare room was so simple, and I connected with a responsible tenant within days. The platform made it easy to communicate and set expectations before meeting in person.",
    author: {
      name: "Emre Yılmaz",
      role: "Local Istanbul Resident",
      image: "https://source.unsplash.com/random/100x100/?portrait,man,2"
    }
  },
  {
    id: 5,
    content: "After accepting a job in Istanbul, I needed to find housing quickly. Istanbul Rooms helped me secure a private room in a beautiful shared apartment with amazing roommates who've now become close friends.",
    author: {
      name: "Carlos Mendoza",
      role: "Software Engineer",
      image: "https://source.unsplash.com/random/100x100/?portrait,man,3"
    }
  }
];

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const testimonialsRef = useRef(null);

  const handleDotClick = (index) => {
    setActiveIndex(index);
    if (testimonialsRef.current) {
      const slideWidth = testimonialsRef.current.clientWidth;
      testimonialsRef.current.scrollTo({
        left: slideWidth * index,
        behavior: 'smooth'
      });
    }
  };

  const handleScroll = () => {
    if (testimonialsRef.current) {
      const slideWidth = testimonialsRef.current.clientWidth;
      const newIndex = Math.round(testimonialsRef.current.scrollLeft / slideWidth);
      setActiveIndex(newIndex);
    }
  };

  return (
    <section className="py-16 bg-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl">
            What Our Users Say
          </h2>
          <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600">
            Stories from people who've found their perfect home with Istanbul Rooms
          </p>
        </div>

        {/* Quote Icon */}
        <div className="flex justify-center mb-8">
          <svg className="h-12 w-12 text-blue-200" fill="currentColor" viewBox="0 0 32 32" aria-hidden="true">
            <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
          </svg>
        </div>

        {/* Testimonial Carousel */}
        <div className="relative">
          <div 
            ref={testimonialsRef}
            className="flex overflow-x-auto snap-x snap-mandatory hide-scrollbar"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            onScroll={handleScroll}
          >
            {testimonials.map((testimonial) => (
              <div 
                key={testimonial.id}
                className="flex-none w-full snap-center px-4"
              >
                <div className="bg-white rounded-xl shadow-md p-8 max-w-3xl mx-auto">
                  <div className="text-xl text-gray-800 italic leading-relaxed mb-8">
                    "{testimonial.content}"
                  </div>
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full overflow-hidden bg-gray-200">
                      <div 
                        className="h-full w-full bg-cover bg-center"
                        style={{ backgroundImage: `url(${testimonial.author.image})` }}
                      />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-bold text-gray-900">{testimonial.author.name}</h4>
                      <p className="text-gray-600">{testimonial.author.role}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                className={`h-3 w-3 rounded-full ${
                  activeIndex === index ? 'bg-blue-600' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;