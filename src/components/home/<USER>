// components/home/<USER>
import Link from 'next/link';

const CallToAction = () => {
  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0">
        <div 
          className="absolute inset-0 bg-cover bg-center opacity-20"
          style={{ 
            backgroundImage: "url('https://source.unsplash.com/random/1920x1080/?istanbul,skyline')" 
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-700 mix-blend-multiply" />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
            Ready to Find Your New Home in Istanbul?
          </h2>
          <p className="mt-4 text-xl text-blue-100">
            Join thousands of people who've found their perfect living situation through Istanbul Rooms.
          </p>
          
          <div className="mt-10 flex flex-col sm:flex-row sm:justify-center gap-4">
            <Link 
              href="/search" 
              className="px-8 py-4 text-lg font-medium rounded-md shadow-lg text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-700 focus:ring-white"
            >
              Find a Room
            </Link>
            <Link 
              href="/listings/create" 
              className="px-8 py-4 text-lg font-medium rounded-md shadow-lg text-white bg-blue-800 bg-opacity-60 hover:bg-opacity-70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-700 focus:ring-blue-400"
            >
              List Your Space
            </Link>
          </div>
        </div>
        
        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 gap-8 md:grid-cols-4">
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-4xl font-extrabold text-white">5000+</div>
            <div className="mt-2 text-blue-100">Active Listings</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-4xl font-extrabold text-white">8500+</div>
            <div className="mt-2 text-blue-100">Happy Users</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-4xl font-extrabold text-white">15+</div>
            <div className="mt-2 text-blue-100">Istanbul Districts</div>
          </div>
          <div className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-sm rounded-lg p-6 text-center">
            <div className="text-4xl font-extrabold text-white">4.8/5</div>
            <div className="mt-2 text-blue-100">User Rating</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;