// components/layout/Header.jsx
import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { data: session, status } = useSession();

  return (
    <header className="sticky top-0 bg-white shadow-sm z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link href="/">
              <div className="flex items-center cursor-pointer">
                <span className="text-3xl font-bold text-blue-800">Homieye</span>
              </div>
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden md:flex space-x-12 items-center">
            <div className="text-center">
              <Link href="/search?type=shared" className="text-gray-600 hover:text-blue-800 transition-colors duration-200">
                <div className="font-medium">Shared</div>
                <div className="text-sm text-gray-500">Apartments</div>
              </Link>
            </div>
            <div className="text-center">
              <Link href="/search?type=private" className="text-gray-600 hover:text-blue-800 transition-colors duration-200">
                <div className="font-medium">Private</div>
                <div className="text-sm text-gray-500">Rooms</div>
              </Link>
            </div>
            <div className="text-center">
              <Link href="/search?type=short" className="text-gray-600 hover:text-blue-800 transition-colors duration-200">
                <div className="font-medium">Short</div>
                <div className="text-sm text-gray-500">Stays</div>
              </Link>
            </div>
            <div className="text-center">
              <Link href="/roommates" className="text-gray-600 hover:text-blue-800 transition-colors duration-200">
                <div className="font-medium">Find</div>
                <div className="text-sm text-gray-500">Roommates</div>
              </Link>
            </div>
          </nav>

          {/* User Profile Button - Desktop */}
          <div className="hidden md:flex items-center">
            {session ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center focus:outline-none bg-blue-800 text-white px-6 py-3 rounded-full hover:bg-blue-900 transition-colors duration-200"
                >
                  <span className="font-medium">
                    {session.user?.name || `${session.user?.firstName || ''} ${session.user?.lastName || ''}`.trim() || 'User'}
                  </span>
                </button>

                {/* Dropdown Menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 ring-1 ring-black ring-opacity-5">
                    <Link href="/dashboard" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Dashboard
                    </Link>
                    <Link href="/account" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      Profile
                    </Link>
                    <Link href="/listings/create" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      List Your Space
                    </Link>
                    <div className="border-t border-gray-100"></div>
                    <button
                      onClick={() => signOut()}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/auth/login"
                className="bg-blue-800 text-white px-6 py-3 rounded-full hover:bg-blue-900 transition-colors duration-200 font-medium"
              >
                Sign In
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none"
            >
              <span className="sr-only">Open main menu</span>
              {isMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link href="/search?type=shared" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
              Shared Apartments
            </Link>
            <Link href="/search?type=private" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
              Private Rooms
            </Link>
            <Link href="/search?type=short" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
              Short Stays
            </Link>
            <Link href="/roommates" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
              Find Roommates
            </Link>
          </div>
          <div className="pt-4 pb-3 border-t border-gray-200">
            {session ? (
              <>
                <div className="flex items-center px-5">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-blue-800 flex items-center justify-center text-white font-medium">
                      {session.user?.name ? session.user.name.charAt(0).toUpperCase() : 'U'}
                    </div>
                  </div>
                  <div className="ml-3">
                    <div className="text-base font-medium text-gray-800">
                      {session.user?.name || `${session.user?.firstName || ''} ${session.user?.lastName || ''}`.trim() || 'User'}
                    </div>
                    <div className="text-sm font-medium text-gray-500">{session.user?.email}</div>
                  </div>
                </div>
                <div className="mt-3 space-y-1 px-2">
                  <Link href="/dashboard" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
                    Dashboard
                  </Link>
                  <Link href="/account" className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
                    Profile
                  </Link>
                  <button
                    onClick={() => signOut()}
                    className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50"
                  >
                    Sign Out
                  </button>
                </div>
              </>
            ) : (
              <div className="px-5 py-3">
                <Link
                  href="/auth/login"
                  className="block w-full text-center bg-blue-800 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-900 transition-colors duration-200"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/register"
                  className="block w-full text-center mt-2 border border-blue-800 text-blue-800 px-4 py-2 rounded-md font-medium hover:bg-blue-50 transition-colors duration-200"
                >
                  Register
                </Link>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;