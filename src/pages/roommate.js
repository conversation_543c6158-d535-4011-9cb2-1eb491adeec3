// pages/roommates.js
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../components/layout/Layout';
import RoommateFilter from '../components/roommates/RoommateFilter';
import RoommateCard from '../components/roommates/RoommateCard';
import { dummyRoommates } from '../utils/dummyData';

export default function RoommatesSearch() {
  const router = useRouter();
  const [filteredRoommates, setFilteredRoommates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortBy, setSortBy] = useState('recent');
  
  // Filter roommates based on URL query params
  useEffect(() => {
    if (!router.isReady) return;
    
    // There's a slight delay before router.query is populated
    setIsLoading(true);
    
    // Create filters object from URL query params
    const filters = {
      q: router.query.q || '',
      gender: router.query.gender || 'any',
      ageMin: router.query.ageMin || '',
      ageMax: router.query.ageMax || '',
      budgetMin: router.query.budgetMin || '',
      budgetMax: router.query.budgetMax || '',
      moveInDate: router.query.moveInDate || '',
      smoking: router.query.smoking || 'any',
      pets: router.query.pets || 'any',
      verified: router.query.verified === 'true',
    };
    
    // Filter roommates
    let results = filterRoommates(dummyRoommates, filters);
    
    // Sort roommates
    results = sortRoommates(results, sortBy);
    
    setFilteredRoommates(results);
    setIsLoading(false);
  }, [router.isReady, router.query, sortBy]);
  
  // Filter function
  const filterRoommates = (roommates, filters) => {
    return roommates.filter(roommate => {
      // Filter by gender
      if (filters.gender !== 'any' && roommate.gender.toLowerCase() !== filters.gender) {
        return false;
      }
      
      // Filter by age range
      if (filters.ageMin && roommate.age < parseInt(filters.ageMin)) {
        return false;
      }
      if (filters.ageMax && roommate.age > parseInt(filters.ageMax)) {
        return false;
      }
      
      // Filter by budget range
      const budgetValue = parseInt(roommate.budget.replace(/[^0-9]/g, ''));
      if (filters.budgetMin && budgetValue < parseInt(filters.budgetMin)) {
        return false;
      }
      if (filters.budgetMax && budgetValue > parseInt(filters.budgetMax)) {
        return false;
      }
      
      // Filter by move-in date
      if (filters.moveInDate && new Date(filters.moveInDate) > new Date(roommate.moveInDate)) {
        return false;
      }
      
      // Filter by smoking preference
      if (filters.smoking === 'yes' && !roommate.smoking) {
        return false;
      }
      if (filters.smoking === 'no' && roommate.smoking) {
        return false;
      }
      
      // Filter by pet preference
      if (filters.pets === 'yes' && !roommate.pets) {
        return false;
      }
      if (filters.pets === 'no' && roommate.pets) {
        return false;
      }
      
      // Filter by verification status
      if (filters.verified && !roommate.verified) {
        return false;
      }
      
      // Filter by search query
      if (filters.q) {
        const query = filters.q.toLowerCase();
        const matchesName = roommate.name.toLowerCase().includes(query);
        const matchesLocation = roommate.location.toLowerCase().includes(query);
        const matchesOccupation = roommate.occupation.toLowerCase().includes(query);
        const matchesBio = roommate.bio.toLowerCase().includes(query);
        
        if (!matchesName && !matchesLocation && !matchesOccupation && !matchesBio) {
          return false;
        }
      }
      
      return true;
    });
  };
  
  // Sort function
  const sortRoommates = (roommates, sortBy) => {
    switch (sortBy) {
      case 'recent':
        // Sort by activity, more recent first
        return [...roommates].sort((a, b) => {
          const timeA = a.lastActive.includes('hour') ? 1 :
                        a.lastActive.includes('day') ? 24 :
                        a.lastActive.includes('week') ? 168 : 720;
          const timeB = b.lastActive.includes('hour') ? 1 :
                        b.lastActive.includes('day') ? 24 :
                        b.lastActive.includes('week') ? 168 : 720;
          return timeA - timeB;
        });
      case 'budgetLow':
        // Sort by budget, lowest first
        return [...roommates].sort((a, b) => {
          const budgetA = parseInt(a.budget.replace(/[^0-9]/g, ''));
          const budgetB = parseInt(b.budget.replace(/[^0-9]/g, ''));
          return budgetA - budgetB;
        });
      case 'budgetHigh':
        // Sort by budget, highest first
        return [...roommates].sort((a, b) => {
          const budgetA = parseInt(a.budget.replace(/[^0-9]/g, ''));
          const budgetB = parseInt(b.budget.replace(/[^0-9]/g, ''));
          return budgetB - budgetA;
        });
      case 'ageYoung':
        // Sort by age, youngest first
        return [...roommates].sort((a, b) => a.age - b.age);
      case 'ageOld':
        // Sort by age, oldest first
        return [...roommates].sort((a, b) => b.age - a.age);
      default:
        return roommates;
    }
  };
  
  const handleFilterChange = (newFilters) => {
    // This is called when filters are applied
    // The actual filtering happens in the useEffect above when router.query changes
    setIsLoading(true);
  };
  
  const handleSortChange = (e) => {
    setSortBy(e.target.value);
  };
  
  return (
    <Layout>
      <Head>
        <title>Find Roommates in Istanbul | Istanbul Rooms</title>
        <meta name="description" content="Connect with potential roommates in Istanbul. Find the perfect person to share your apartment with based on lifestyle, budget, and preferences." />
      </Head>
      
      <div className="bg-gray-100 min-h-screen py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Find Your Ideal Roommate in Istanbul
            </h1>
            <p className="mt-4 max-w-3xl mx-auto text-xl text-gray-600">
              Connect with people looking to share apartments in Istanbul. Filter by preferences and get in touch.
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full lg:w-1/4">
              <RoommateFilter 
                initialFilters={router.query}
                onFilterChange={handleFilterChange}
              />
              
              {/* Quick Tips */}
              <div className="mt-6 bg-white rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Roommate Search Tips</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Look for verified profiles for added security
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Check activity status - recently active users respond faster
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Be specific about your own preferences when messaging
                  </li>
                </ul>
                <a href="/safety" className="mt-3 text-blue-600 hover:text-blue-800 text-sm inline-flex items-center">
                  Read our full safety guide
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="w-full lg:w-3/4">
              {/* Sorting Controls */}
              <div className="bg-white rounded-lg shadow-md p-4 mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                  <p className="text-sm text-gray-600">
                    {isLoading 
                      ? 'Finding roommates...'
                      : `Found ${filteredRoommates.length} potential roommates`}
                  </p>
                  
                  {/* Sort Controls */}
                  <div className="flex items-center">
                    <label htmlFor="sort" className="text-sm font-medium text-gray-700 mr-2">Sort by:</label>
                    <select
                      id="sort"
                      value={sortBy}
                      onChange={handleSortChange}
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    >
                      <option value="recent">Recently Active</option>
                      <option value="budgetLow">Budget: Low to High</option>
                      <option value="budgetHigh">Budget: High to Low</option>
                      <option value="ageYoung">Age: Youngest First</option>
                      <option value="ageOld">Age: Oldest First</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Loading State */}
              {isLoading && (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              )}
              
              {/* No Results */}
              {!isLoading && filteredRoommates.length === 0 && (
                <div className="bg-white rounded-lg shadow-md p-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="mt-4 text-lg font-medium text-gray-900">No roommates found</h3>
                  <p className="mt-2 text-gray-600">
                    Try adjusting your filters or search criteria.
                  </p>
                </div>
              )}
              
              {/* Roommate Grid */}
              {!isLoading && filteredRoommates.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                  {filteredRoommates.map((roommate) => (
                    <div key={roommate.id}>
                      <RoommateCard roommate={roommate} />
                    </div>
                  ))}
                </div>
              )}
              
              {/* Create Profile CTA */}
              <div className="mt-12 bg-blue-50 rounded-lg shadow-md p-8">
                <div className="text-center">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Looking for a roommate yourself?</h3>
                  <p className="text-gray-600 mb-6">
                    Create your own profile and let potential roommates find you. It's free and only takes a few minutes.
                  </p>
                  <Link 
                    href="/roommates/create-profile" 
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Create Roommate Profile
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}