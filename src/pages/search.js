// pages/search.js
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Layout from '../components/layout/Layout';
import ListingCard from '../components/search/ListingCard';
import FilterPanel from '../components/search/FilterPanel';
import MapView from '../components/search/MapView';
import SimpleMapView from '../components/search/SimpleMapView';
import CommuteSearch from '../components/search/CommuteSearch';
import SaveSearchModal from '../components/search/SaveSearchModal';
import SavedSearches from '../components/search/SavedSearches';
import { useAuth } from '../utils/authContext';
import { filterListingsByCommuteTime } from '../utils/commuteUtils';
import { getUserLocation } from '../utils/geoUtils';

export default function Search() {
  const router = useRouter();
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'map'
  const [listings, setListings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [sortBy, setSortBy] = useState('featured');
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(1);
  const [showSaveSearchModal, setShowSaveSearchModal] = useState(false);
  const [userLocation, setUserLocation] = useState(null);
  const [commuteParams, setCommuteParams] = useState({});
  const [locationName, setLocationName] = useState('');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [locationError, setLocationError] = useState('');
  const [selectedPlace, setSelectedPlace] = useState(null);

  // Define getLocation function at the top level of the component
  const getLocation = async () => {
    // Set loading state
    setIsLoading(true);
    
    try {
      console.log('Requesting user location...');
      
      // Request user's location
      const location = await getUserLocation();
      setUserLocation(location);
      console.log('Got user location:', location);
      
      // Get city name from coordinates using reverse geocoding
      try {
        const response = await fetch(`/api/geocode/reverse?lat=${location.lat}&lng=${location.lng}`);
        const data = await response.json();
        
        if (data.success && data.city) {
          console.log('Found location:', data.city, data.country);
          
          // Update URL with user's city without full page reload
          const newQuery = { ...router.query };
          delete newQuery.useLocation; // Remove the useLocation flag
          
          // Set city filter based on the reverse geocoding result
          newQuery.city = data.city;
          
          // Add user's coordinates to ensure proximity filtering
          newQuery.userLat = location.lat;
          newQuery.userLng = location.lng;
          newQuery.radius = 100; // 100km radius by default
          
          // Set sorting to distance by default for location-based searches
          newQuery.sortBy = 'distance';
          
          // Clear any existing q parameter to avoid conflicts
          delete newQuery.q;
          
          // Save the location name for the page title
          setLocationName(data.city);
          console.log('Setting location name to:', data.city);
          
          // Update the URL and trigger a search with the new city
          router.push({
            pathname: '/search',
            query: newQuery
          }, undefined, { shallow: true });
        }
      } catch (geocodeError) {
        console.error('Error getting city from coordinates:', geocodeError);
        setError('Could not determine your location. Please try again or enter a city manually.');
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error getting user location:', error);
      setError('Could not access your location. Please check your browser permissions and try again.');
      setIsLoading(false);
    }
  };

  const limit = 20; // Number of listings per page

  // Automatically request location on page load if no specific location is provided
  useEffect(() => {
    if (router.isReady) {
      // If no specific location parameters are provided, show all listings first
      if (!router.query.city && !router.query.q && !router.query.lat && !router.query.lng && !router.query.useLocation) {
        // Set location name to show all listings
        setLocationName('All Areas');

        // Check if we already have permission to access location
        navigator.permissions?.query({ name: 'geolocation' })
          .then(permissionStatus => {
            if (permissionStatus.state === 'granted') {
              // We already have permission, so get the location automatically
              // But don't filter by it initially - just use for distance calculation
              getUserLocation().then(location => {
                setUserLocation(location);
                console.log('Got user location for distance calculation:', location);
              }).catch(error => {
                console.log('Could not get user location:', error);
              });
            }
          })
          .catch(() => {
            // If we can't check permissions, don't auto-request location
            console.log('Could not check geolocation permissions');
          });
      }
    }
  }, [router.isReady, router.query]);
  
  // Handle explicit location requests via the "Use My Location" button or URL param
  useEffect(() => {
    // Only attempt to get location if explicitly requested via URL param
    if (router.isReady && router.query.useLocation === 'true') {
      const getLocationFromParam = async () => {
        setIsLoadingLocation(true);
        setLocationError('');
        
        try {
          // Get user's exact location using the utility function
          const location = await getUserLocation();
          const { lat, lng } = location;
          
          console.log('Got exact user location:', lat, lng);
          
          // Get location name from coordinates
          const response = await fetch(`/api/geocode/reverse?lat=${lat}&lng=${lng}`);
          const data = await response.json();
          
          if (data.success) {
            console.log('Reverse geocoded location:', data);
            
            // Update location state with exact coordinates
            setUserLocation({
              lat: lat,
              lng: lng,
              city: data.city,
              country: data.country,
              formattedAddress: data.formattedAddress
            });
            
            // Update URL parameters
            const newQuery = { ...router.query, useLocation: 'true' };
            delete newQuery.city; // Remove city parameter to avoid conflicts
            delete newQuery.neighborhood; // Remove neighborhood parameter
            
            router.push({
              pathname: '/search',
              query: newQuery
            }, undefined, { shallow: true });
          } else {
            setLocationError('Could not determine your location');
          }
        } catch (error) {
          console.error('Error getting user location:', error);
          setLocationError('Could not access your location. Please check your browser permissions and try again.');
        } finally {
          setIsLoadingLocation(false);
        }
      };
      
      getLocationFromParam();
    }
  }, [router.isReady, router.query.useLocation]);

  // Get location from URL parameters
  const getLocationFromParam = async () => {
    setIsLoadingLocation(true);
    setLocationError('');
    
    try {
      // Get user's exact location using the utility function
      const location = await getUserLocation();
      const { lat, lng } = location;
      
      console.log('Got exact user location:', lat, lng);
      
      // Get detailed location information from coordinates
      const response = await fetch(`/api/geocode/reverse?lat=${lat}&lng=${lng}`);
      const data = await response.json();
      
      if (data.success) {
        console.log('Reverse geocoded location with details:', data);
        
        // Update location state with exact coordinates and detailed information
        setUserLocation({
          lat: lat,
          lng: lng,
          city: data.city,
          country: data.country,
          neighborhood: data.neighborhood,
          street: data.street,
          exactLocation: data.exactLocation,
          formattedAddress: data.formattedAddress
        });
        
        // Set location name to the detailed address
        setLocationName(data.formattedAddress);
        
        // Display the full address in the document title and header
        document.title = `Listings near ${data.formattedAddress} | RoomShare`;
        
        // Update URL parameters with exact location information
        const newQuery = { 
          ...router.query, 
          useLocation: 'true',
          userLat: lat,
          userLng: lng,
          exactLocation: 'true'
        };
        
        // Remove conflicting parameters
        delete newQuery.city;
        delete newQuery.neighborhood;
        
        // Update sorting to distance by default for location-based searches
        newQuery.sortBy = 'distance';
        
        router.push({
          pathname: '/search',
          query: newQuery
        }, undefined, { shallow: true });
      } else {
        setLocationError('Could not determine your exact location');
      }
    } catch (error) {
      console.error('Location error:', error);
      setLocationError('An error occurred while getting your location: ' + error.message);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Handle location button click
  const handleLocationClick = async () => {
    setIsLoadingLocation(true);
    setLocationError('');
    
    try {
      // Get user's exact location using the utility function
      const location = await getUserLocation();
      const { lat, lng } = location;
      
      console.log('Got exact user location:', lat, lng);
      
      // Get detailed location information from coordinates
      const response = await fetch(`/api/geocode/reverse?lat=${lat}&lng=${lng}`);
      const data = await response.json();
      
      if (data.success) {
        console.log('Reverse geocoded location with details:', data);
        
        // Update location state with exact coordinates and detailed information
        setUserLocation({
          lat: lat,
          lng: lng,
          city: data.city,
          country: data.country,
          neighborhood: data.neighborhood,
          street: data.street,
          exactLocation: data.exactLocation,
          formattedAddress: data.formattedAddress
        });
        
        // Set location name to the detailed address
        setLocationName(data.formattedAddress);
        
        // Display the full address in the document title and header
        document.title = `Listings near ${data.formattedAddress} | RoomShare`;
        
        // Update URL parameters with exact location information
        const newQuery = { 
          ...router.query, 
          useLocation: 'true',
          userLat: lat,
          userLng: lng,
          exactLocation: 'true'
        };
        
        // Remove conflicting parameters
        delete newQuery.city;
        delete newQuery.neighborhood;
        
        // Update sorting to distance by default for location-based searches
        newQuery.sortBy = 'distance';
        
        router.push({
          pathname: '/search',
          query: newQuery
        }, undefined, { shallow: true });
      } else {
        setLocationError('Could not determine your exact location');
      }
    } catch (error) {
      console.error('Location error:', error);
      setLocationError('An error occurred while getting your location: ' + error.message);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Extract commute parameters from URL if present
  useEffect(() => {
    if (router.isReady) {
      const { 
        commuteAddress, 
        commuteTime, 
        commuteMode, 
        commuteLat, 
        commuteLng,
        commuteRadius 
      } = router.query;
      
      if (commuteAddress) {
        const params = {
          commuteAddress,
          commuteTime: commuteTime ? parseInt(commuteTime) : 30,
          commuteMode: commuteMode || 'transit',
          radius: commuteRadius ? parseInt(commuteRadius) : 10
        };
        
        // Add coordinates if present
        if (commuteLat && commuteLng) {
          params.coordinates = {
            lat: parseFloat(commuteLat),
            lng: parseFloat(commuteLng)
          };
        }
        
        setCommuteParams(params);
      }
    }
  }, [router.isReady, router.query]);
  
  // Update location name from URL parameters
  useEffect(() => {
    if (router.isReady) {
      // Set location name based on URL parameters
      if (router.query.city) {
        setLocationName(router.query.city);
      } else if (router.query.q) {
        setLocationName(router.query.q);
      } else {
        setLocationName('Your Area');
      }
      
      // If no location parameters are set, try to get user's location automatically
      if (!router.query.city && !router.query.q && !router.query.lat && !router.query.lng && !userLocation) {
        // Automatically request location
        getLocation();
      }
    }
  }, [router.isReady, router.query.city, router.query.q, userLocation]);
  
  // Extract filter parameters from URL query and fetch listings
  useEffect(() => {
    if (!router.isReady) return;
    
    // If no specific location is set and we're not already loading location data,
    // set a default location name to avoid showing "Istanbul"
    if (!router.query.city && !router.query.q && !router.query.lat && !router.query.lng && !isLoading && !locationName) {
      setLocationName('Your Area');
    }
    
    const fetchListings = async () => {
      try {
        setIsLoading(true);
        setError('');
        
        // Build query parameters
        const params = new URLSearchParams();
        
        // Add all query parameters from URL
        Object.entries(router.query).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value);
          }
        });
        
        // Add pagination
        params.append('limit', limit.toString());
        params.append('skip', ((page - 1) * limit).toString());
        
        // Add sorting
        params.append('sortBy', sortBy === 'featured' ? 'featured' : 
                              sortBy === 'priceAsc' ? 'price' : 
                              sortBy === 'priceDesc' ? 'price' : 'createdAt');
        params.append('sortOrder', sortBy === 'priceAsc' ? 'asc' : 'desc');
        
        // Add user location if available for distance-based sorting
        if (userLocation) {
          params.append('userLat', userLocation.lat.toString());
          params.append('userLng', userLocation.lng.toString());
        }
        
        // Fetch listings from API
        const response = await fetch(`/api/listings/search?${params.toString()}`);
        const data = await response.json();
        
        if (response.ok) {
          let filteredListings = data.data;
          
          // Apply commute time filtering if needed
          if (Object.keys(commuteParams).length > 0) {
            setIsLoading(true); // Keep loading state while filtering by commute time
            filteredListings = await filterListingsByCommuteTime(filteredListings, commuteParams);
          }
          
          // Process listings to ensure they have all required fields
          const processedListings = filteredListings.map(listing => {
            // Add default image if missing
            if (!listing.images || listing.images.length === 0) {
              listing.images = [{ url: '/images/placeholder.jpg', isMain: true }];
            }
            
            // Add placeholder for missing coordinates
            if (!listing.address || !listing.address.location || !listing.address.location.coordinates) {
              listing.address = {
                ...listing.address,
                location: {
                  coordinates: [28.9784, 41.0082] // Default to Istanbul center
                }
              };
            }
            
            // Ensure price object exists
            if (!listing.price) {
              listing.price = { amount: 0, currency: 'TRY', paymentPeriod: 'monthly' };
            }
            
            return listing;
          });
          
          setListings(processedListings);
          setTotalCount(data.totalCount);
        } else {
          setError(data.message || 'Failed to fetch listings');
        }
      } catch (err) {
        console.error('Error fetching listings:', err);
        setError('An error occurred while fetching listings');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchListings();
  }, [router.isReady, router.query, page, sortBy, commuteParams, userLocation]);
  
  const handleFilterChange = (newFilters) => {
    // Check if we have a selected place from the filter panel
    if (newFilters.selectedPlace) {
      setSelectedPlace(newFilters.selectedPlace);
      console.log('Selected place from filters:', newFilters.selectedPlace);
    }
    
    setFilters(newFilters);
    fetchListings(1, newFilters);
  };
  
  const handleSortChange = (e) => {
    setSortBy(e.target.value);
  };
  
  const openSaveSearchModal = () => {
    setShowSaveSearchModal(true);
  };
  
  const handleCommuteChange = (params) => {
    setCommuteParams(params);
    
    // If commute parameters are set, update the URL
    if (Object.keys(params).length > 0) {
      const newQuery = { ...router.query };
      
      // Add commute parameters to the URL
      if (params.commuteAddress) newQuery.commuteAddress = params.commuteAddress;
      if (params.commuteTime) newQuery.commuteTime = params.commuteTime;
      if (params.commuteMode) newQuery.commuteMode = params.commuteMode;
      if (params.coordinates) {
        newQuery.commuteLat = params.coordinates.lat;
        newQuery.commuteLng = params.coordinates.lng;
      }
      if (params.radius) newQuery.commuteRadius = params.radius;
      
      // Update the URL without reloading the page
      router.push({
        pathname: '/search',
        query: newQuery
      }, undefined, { shallow: true });
    } else {
      // If commute parameters are cleared, remove them from the URL
      const newQuery = { ...router.query };
      delete newQuery.commuteAddress;
      delete newQuery.commuteTime;
      delete newQuery.commuteMode;
      delete newQuery.commuteLat;
      delete newQuery.commuteLng;
      delete newQuery.commuteRadius;
      
      // Update the URL without reloading the page
      router.push({
        pathname: '/search',
        query: newQuery
      }, undefined, { shallow: true });
    }
  };
  
  // Calculate total pages for pagination
  const totalPages = Math.ceil(totalCount / limit);
  return (
    <Layout>
      <Head>
        <title>{`${userLocation && userLocation.formattedAddress 
                  ? `Listings near ${userLocation.formattedAddress}` 
                  : locationName 
                    ? `Listings near ${locationName}` 
                    : 'Search Listings'} | RoomShare`}</title>
        <meta name="description" content={`Find your perfect room or apartment in ${locationName || 'your area'}`} />
      </Head>
      
      <div className="bg-gray-100 min-h-screen py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Search Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {userLocation && userLocation.formattedAddress 
                ? `Listings near ${userLocation.formattedAddress}` 
                : locationName 
                  ? `Listings near ${locationName}` 
                  : 'All Listings in Your Area'}
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              {isLoading 
                ? 'Finding the perfect place for you...'
                : listings.length > 0 
                  ? `Found ${listings.length} places to stay`
                  : 'No listings found'}
            </p>
            
            <div className="mt-4 flex md:mt-0 md:ml-4">
              <button
                type="button"
                onClick={handleLocationClick}
                className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5 9V7a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 002 2v2a2 2 0 002-2v-2a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 012-2V9z" clipRule="evenodd" />
                </svg>
                Use My Location
              </button>
              <button
                type="button"
                onClick={() => setViewMode('list')}
                className={`inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium ${viewMode === 'list' ? 'bg-indigo-600 text-white border-transparent' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`}
              >
                <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                List
              </button>
              <button
                type="button"
                onClick={() => setViewMode('map')}
                className={`ml-3 inline-flex items-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium ${viewMode === 'map' ? 'bg-indigo-600 text-white border-transparent' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'}`}
              >
                <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clipRule="evenodd" />
                </svg>
                Map
              </button>
            </div>
          </div>
  
          <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-4">
            <div className="lg:col-span-1">
              <FilterPanel />
              <CommuteSearch onCommuteChange={handleCommuteChange} initialValues={commuteParams} />
              {user && <SavedSearches />}
            </div>
  
            <div className="lg:col-span-3">
              <div className="bg-white shadow rounded-lg p-4 mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-600">
                    Showing {listings.length} of {totalCount} listings
                    {locationName && locationName !== 'Your Area' && locationName !== 'All Areas' && (
                      <span className="ml-1">in {locationName}</span>
                    )}
                    {locationName === 'All Areas' && (
                      <span className="ml-1">across all areas</span>
                    )}
                    {commuteParams.commuteAddress && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                        Filtered by commute time
                      </span>
                    )}
                    {/* Show location badge if we have city or neighborhood filter */}
                    {(router.query.city || router.query.neighborhood) && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {router.query.neighborhood ? `${router.query.neighborhood}, ${router.query.city || ''}` : router.query.city}
                      </span>
                    )}
                    {userLocation && (
                      <div className="inline-flex flex-col items-start">
                        <div className="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mr-2 mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                          </svg>
                          Using your exact location
                        </div>
                        
                        {userLocation.formattedAddress && (
                          <div className="text-sm text-gray-700 font-medium ml-2 mb-1">
                            <span className="inline-block mr-1">📍</span>
                            {userLocation.formattedAddress}
                          </div>
                        )}
                        
                        {userLocation.exactLocation && (
                          <div className="text-xs text-blue-600 ml-2">
                            <span className="inline-block mr-1">🌐</span>
                            {userLocation.exactLocation}
                          </div>
                        )}
                      </div>
                    )}
                  </p>
                </div>
                  <div className="flex items-center">
                  {/* Location-based search button */}
                  <button
                    onClick={() => {
                      if (userLocation) {
                        // If we already have location, update filters with it
                        setIsLoading(true);
                        fetch(`/api/geocode/reverse?lat=${userLocation.lat}&lng=${userLocation.lng}`)
                          .then(res => res.json())
                          .then(data => {
                            if (data.success && data.city) {
                              console.log('Using location:', data.city, data.country);

                              // Update URL with user's city
                              const newQuery = { ...router.query };

                              // Clear any existing location filters
                              delete newQuery.q;

                              // Set the city from the geocoding result
                              newQuery.city = data.city;

                              // Add user's coordinates to ensure proximity filtering
                              newQuery.userLat = userLocation.lat;
                              newQuery.userLng = userLocation.lng;
                              newQuery.radius = 50; // 50km radius by default

                              // Set sorting to distance by default for location-based searches
                              newQuery.sortBy = 'distance';

                              // Save the location name for the page title
                              setLocationName(data.city);

                              router.push({
                                pathname: '/search',
                                query: newQuery
                              }, undefined, { shallow: true });
                            }
                          })
                          .catch(error => {
                            console.error('Error getting city:', error);
                            setError('Could not determine your location. Please try again or enter a city manually.');
                          })
                          .finally(() => setIsLoading(false));
                      } else {
                        // Request location access
                        setError(''); // Clear any previous errors
                        router.push({
                          pathname: '/search',
                          query: { ...router.query, useLocation: 'true' }
                        }, undefined, { shallow: true });
                      }
                    }}
                    disabled={isLoading}
                    className={`mr-3 inline-flex items-center px-4 py-2 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                      userLocation && (router.query.city || router.query.userLat)
                        ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100 focus:ring-blue-500'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Getting Location...
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        {userLocation ? 'Update Location' : 'Use My Location'}
                      </>
                    )}
                  </button>
                  <div>
                    <label htmlFor="sortBy" className="sr-only">Sort by</label>
                    <select
                      id="sortBy"
                      name="sortBy"
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                    >
                      <option value="featured">Featured</option>
                      <option value="newest">Newest</option>
                      <option value="priceAsc">Price: Low to High</option>
                      <option value="priceDesc">Price: High to Low</option>
                      {userLocation && <option value="distance">Distance: Nearest First</option>}
                      {commuteParams.commuteAddress && <option value="commuteTime">Commute Time: Shortest First</option>}
                    </select>
                  </div>
                </div>
                </div>
              </div>
  
              {error && (
                <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}
  
              {isLoading ? (
                <div className="flex justify-center py-12">
                  <svg className="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              ) : viewMode === 'list' ? (
                <div className="space-y-6">
                  {listings.length > 0 ? (
                    listings.map((listing) => (
                      <ListingCard key={listing._id} listing={listing} />
                    ))
                  ) : (
                    <div className="bg-white shadow rounded-lg p-6 text-center">
                      <p className="text-gray-500">No listings found matching your criteria.</p>
                    </div>
                  )}
  
                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="mt-6 flex justify-center">
                      {/* Pagination controls would go here */}
                    </div>
                  )}
                </div>
              ) : (
                /* Map View */
                <div className="h-[calc(100vh-300px)] min-h-[500px]">
                  {viewMode === 'map' && (
                    <SimpleMapView 
                      listings={listings} 
                      userLocation={userLocation} 
                      selectedPlace={selectedPlace}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
