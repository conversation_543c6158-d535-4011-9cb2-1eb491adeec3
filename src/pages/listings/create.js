// pages/listings/create.js
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import CreateListingForm from '../../components/listings/CreateListingForm';
import ProtectedRoute from '../../components/auth/ProtectedRoute';

export default function CreateListingPage() {
  return (
    <ProtectedRoute>
      <Layout>
      <Head>
        <title>Create a New Listing | Istanbul Rooms</title>
        <meta name="description" content="List your apartment, room, or short-stay accommodation on Istanbul Rooms." />
      </Head>

      <div className="bg-gray-100 py-8">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <nav className="flex mb-6" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li>
                <Link href="/" className="hover:text-gray-700">
                  Home
                </Link>
              </li>
              <li>
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </li>
              <li>
                <Link href="/dashboard/listings" className="hover:text-gray-700">
                  My Listings
                </Link>
              </li>
              <li>
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </li>
              <li className="font-medium text-gray-900">
                Create Listing
              </li>
            </ol>
          </nav>

          {/* Create Listing Form */}
          <CreateListingForm />

          {/* Listing Tips */}
          <div className="mt-8 bg-blue-50 rounded-lg shadow-md p-6">
            <h2 className="text-lg font-bold text-gray-900 mb-3">Tips for a Successful Listing</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col items-center text-center p-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600 mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">High-Quality Photos</h3>
                <p className="text-sm text-gray-600">
                  Upload clear, well-lit photos from different angles. Show all rooms and highlight special features.
                </p>
              </div>

              <div className="flex flex-col items-center text-center p-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600 mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">Detailed Description</h3>
                <p className="text-sm text-gray-600">
                  Be honest and thorough. Mention the property's condition, nearby attractions, and transportation options.
                </p>
              </div>

              <div className="flex flex-col items-center text-center p-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600 mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">Respond Quickly</h3>
                <p className="text-sm text-gray-600">
                  Reply to inquiries promptly. Fast responses increase your chances of finding the right tenant.
                </p>
              </div>
            </div>

            <div className="mt-4 text-center">
              <Link href="/listing-guidelines" className="text-blue-600 hover:text-blue-800 font-medium">
                Read our full listing guidelines
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
    </ProtectedRoute>
  );
}