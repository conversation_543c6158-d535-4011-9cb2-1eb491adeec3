// pages/roommates/[id].js
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';
import { dummyRoommates, dummyListings } from '../../utils/dummyData';

export default function RoommateDetail() {
  const router = useRouter();
  const { id } = router.query;
  const [roommate, setRoommate] = useState(null);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [messageOpen, setMessageOpen] = useState(false);
  const [message, setMessage] = useState('');
  
  // Fetch roommate data
  useEffect(() => {
    if (id) {
      setIsLoading(true);
      // In a real app, this would be an API call
      const foundRoommate = dummyRoommates.find(item => item.id === id);
      
      if (foundRoommate) {
        setRoommate(foundRoommate);
      } else {
        // Handle not found
        console.error('Roommate profile not found');
      }
      
      setIsLoading(false);
    }
  }, [id]);
  
  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // In a real app, save to user's favorites
  };
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    // In a real app, this would send the message
    alert(`Message sent to ${roommate.name}: ${message}`);
    setMessage('');
    setMessageOpen(false);
  };
  
  if (isLoading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }
  
  if (!roommate) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900">Profile Not Found</h2>
            <p className="mt-2 text-gray-600">The roommate profile you're looking for doesn't exist or has been removed.</p>
            <Link href="/roommates" className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
              Back to Roommates
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>{roommate.name} | Roommate Profile | Istanbul Rooms</title>
        <meta name="description" content={`${roommate.name}, ${roommate.age} - ${roommate.occupation}. Looking for a room in ${roommate.location} with a budget of ${roommate.budget}.`} />
      </Head>
      
      <div className="bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Breadcrumbs */}
          <nav className="flex mb-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm text-gray-500">
              <li>
                <Link href="/" className="hover:text-gray-700">
                  Home
                </Link>
              </li>
              <li>
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </li>
              <li>
                <Link href="/roommates" className="hover:text-gray-700">
                  Roommates
                </Link>
              </li>
              <li>
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </li>
              <li className="font-medium text-gray-900 truncate max-w-xs">
                {roommate.name}
              </li>
            </ol>
          </nav>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Profile Header */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="h-64 relative">
                  <div 
                    className="absolute inset-0 bg-cover bg-center" 
                    style={{ backgroundImage: `url(${roommate.image})` }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-70" />
                  
                  {/* Verified Badge */}
                  {roommate.verified && (
                    <div className="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-md flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      Verified
                    </div>
                  )}
                  
                  {/* Info Overlay */}
                  <div className="absolute bottom-0 left-0 p-6 z-10">
                    <h1 className="text-3xl font-bold text-white">{roommate.name}, {roommate.age}</h1>
                    <p className="text-xl text-gray-200">{roommate.occupation}</p>
                    <div className="flex mt-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                        {roommate.gender}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                        Budget: {roommate.budget}
                      </span>
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Move-in: {roommate.moveInDate}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center text-gray-600 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span>Looking in {roommate.location}</span>
                  </div>
                  
                  <h2 className="text-xl font-bold text-gray-900 mb-3">About Me</h2>
                  <p className="text-gray-700 mb-6 leading-relaxed">{roommate.bio}</p>
                  
                  {/* Profile Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-t border-gray-100 pt-6">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Languages</h3>
                      <p className="text-gray-900">{roommate.languages ? roommate.languages.join(', ') : 'English'}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Occupation</h3>
                      <p className="text-gray-900">{roommate.occupation}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Smoking</h3>
                      <p className="text-gray-900">{roommate.smoking ? 'Smoker' : 'Non-smoker'}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Pets</h3>
                      <p className="text-gray-900">{roommate.pets ? 'Has pets' : 'No pets'}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Interests */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Interests</h2>
                <div className="flex flex-wrap gap-2">
                  {roommate.interests.map((interest, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {interest}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Roommate Preferences */}
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Roommate Preferences</h2>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="bg-blue-100 rounded-full p-2 text-blue-600 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Schedule</h3>
                      <p className="text-gray-600 text-sm">{roommate.gender === 'Female' ? 'She' : roommate.gender === 'Male' ? 'He' : 'They'} {roommate.age < 25 ? 'is a student with flexible hours' : 'works regular business hours'} and {Math.random() > 0.5 ? 'is quiet during weeknights' : 'respects personal space and quiet times'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="bg-blue-100 rounded-full p-2 text-blue-600 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Socializing</h3>
                      <p className="text-gray-600 text-sm">{roommate.gender === 'Female' ? 'She' : roommate.gender === 'Male' ? 'He' : 'They'} is {Math.random() > 0.5 ? 'social and enjoys occasional gatherings' : 'respectful of shared spaces and keeps common areas clean'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="bg-blue-100 rounded-full p-2 text-blue-600 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">Cleaning</h3>
                      <p className="text-gray-600 text-sm">{roommate.gender === 'Female' ? 'She' : roommate.gender === 'Male' ? 'He' : 'They'} is {Math.random() > 0.5 ? 'tidy and likes to keep shared spaces clean' : 'organized and happy to participate in a cleaning schedule'}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Similar Profiles */}
              <div className="mt-8 border-t border-gray-200 pt-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Similar Profiles</h2>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {dummyRoommates
                    .filter(item => item.id !== roommate.id)
                    .filter(item => item.gender === roommate.gender || Math.random() > 0.5)
                    .slice(0, 2)
                    .map(similarRoommate => (
                      <Link key={similarRoommate.id} href={`/roommates/${similarRoommate.id}`}>
                        <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                          <div className="h-36 relative">
                            <div 
                              className="absolute inset-0 bg-cover bg-center"
                              style={{ backgroundImage: `url(${similarRoommate.image})` }}
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60" />
                            <div className="absolute bottom-0 left-0 p-4">
                              <h3 className="font-bold text-white">{similarRoommate.name}, {similarRoommate.age}</h3>
                              <p className="text-sm text-gray-200">{similarRoommate.occupation}</p>
                            </div>
                          </div>
                          <div className="p-4">
                            <p className="text-sm text-gray-600">{similarRoommate.location}</p>
                            <p className="mt-1 text-sm font-bold text-blue-600">Budget: {similarRoommate.budget}</p>
                          </div>
                        </div>
                      </Link>
                    ))}
                </div>
              </div>
            </div>
            
            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Card */}
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-20">
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900">Interested in connecting?</h3>
                  <p className="text-gray-600 mt-1">
                    {roommate.name} is looking for a room with a budget of
                  </p>
                  <p className="text-2xl font-bold text-blue-600 mt-1">{roommate.budget}</p>
                </div>
                
                {/* Last Active */}
                <div className="flex items-center justify-center text-sm text-gray-500 mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Last active: {roommate.lastActive}</span>
                </div>
                
                {/* CTA Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={() => setMessageOpen(true)}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                    Message {roommate.name.split(' ')[0]}
                  </button>
                  
                  <button 
                    type="button"
                    onClick={toggleFavorite}
                    className={`w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium ${isFavorite ? 'text-red-600 border-red-300' : 'text-gray-700'} bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-500'}`} viewBox="0 0 24 24" fill={isFavorite ? 'currentColor' : 'none'} stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    {isFavorite ? 'Saved to Favorites' : 'Save to Favorites'}
                  </button>
                  
                  <button 
                    type="button"
                    className="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                    </svg>
                    Share Profile
                  </button>
                </div>
              </div>
              
              {/* Do you have a room? Card */}
              <div className="bg-blue-50 rounded-lg shadow-md p-6">
                <h3 className="font-bold text-gray-900 mb-2">Do you have a room?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  If you have a room or apartment available that matches {roommate.name}'s preferences, let them know!
                </p>
                <Link 
                  href="/listings/create" 
                  className="inline-block w-full text-center py-2 px-4 border border-blue-600 rounded-md text-sm font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  List Your Space
                </Link>
              </div>
              
              {/* Safety Tips */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="font-bold text-gray-900 mb-2">Safety Tips</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Always meet in a public place first
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verify identity with multiple forms of contact
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Trust your instincts when meeting new roommates
                  </li>
                </ul>
                <a href="/safety" className="mt-3 text-blue-600 hover:text-blue-800 text-sm inline-flex items-center">
                  Read our full safety guide
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Message Modal */}
      {messageOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-bold text-gray-900">Message {roommate.name}</h3>
              <button 
                onClick={() => setMessageOpen(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <form onSubmit={handleSendMessage}>
              <div className="mb-4">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Your Message
                </label>
                <textarea
                  id="message"
                  rows={6}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={`Hi ${roommate.name.split(' ')[0]}! I saw your profile and think we might be a good roommate match. Would you like to connect?`}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                ></textarea>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setMessageOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Send Message
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Layout>
  );
}