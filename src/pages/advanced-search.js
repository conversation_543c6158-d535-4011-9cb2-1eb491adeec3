// pages/advanced-search.js
import { useState } from 'react';
import Head from 'next/head';
import Layout from '../components/layout/Layout';
import { dummyListings, filterListings } from '../utils/dummyData';

export default function AdvancedSearch() {
  const [searchFilters, setSearchFilters] = useState({
    query: '',
    type: 'all',
    priceMin: '',
    priceMax: '',
    beds: '',
    baths: '',
    area: '',
    furnished: 'any',
    gender: 'any',
    availableNow: false,
    amenities: []
  });
  
  const [results, setResults] = useState([]);
  const [hasSearched, setHasSearched] = useState(false);
  
  const amenitiesList = [
    'WiFi', 'Air Conditioning', 'Heating', 'Washing Machine', 'Dishwasher', 
    'Balcony', 'Elevator', 'Parking', 'Pet Friendly', 'Near Metro'
  ];
  
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSearchFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  const handleAmenityToggle = (amenity) => {
    setSearchFilters(prev => {
      const currentAmenities = [...prev.amenities];
      
      if (currentAmenities.includes(amenity)) {
        return {
          ...prev,
          amenities: currentAmenities.filter(a => a !== amenity)
        };
      } else {
        return {
          ...prev,
          amenities: [...currentAmenities, amenity]
        };
      }
    });
  };
  
  const handleSearch = (e) => {
    e.preventDefault();
    
    // Filter listings based on criteria
    // In a real app, this would be an API call
    const filtered = filterListings(searchFilters);
    setResults(filtered);
    setHasSearched(true);
  };
  
  return (
    <Layout>
      <Head>
        <title>Advanced Search | Istanbul Rooms</title>
        <meta name="description" content="Use our advanced search features to find your perfect accommodation in Istanbul." />
      </Head>
      
      <div className="bg-gray-100 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900">Advanced Search</h1>
            <p className="mt-4 text-xl text-gray-600">
              Fine-tune your search to find the perfect place in Istanbul
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <form onSubmit={handleSearch} className="p-6">
              <div className="space-y-8">
                {/* Basic Search */}
                <div>
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Search</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="col-span-1 md:col-span-3">
                      <label htmlFor="query" className="block text-sm font-medium text-gray-700 mb-1">
                        Search Term
                      </label>
                      <input
                        type="text"
                        id="query"
                        name="query"
                        value={searchFilters.query}
                        onChange={handleInputChange}
                        placeholder="Keywords, neighborhood, etc."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                        Property Type
                      </label>
                      <select
                        id="type"
                        name="type"
                        value={searchFilters.type}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Types</option>
                        <option value="shared">Shared Apartment</option>
                        <option value="private">Private Room</option>
                        <option value="short">Short Stay</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
                        Gender Preference
                      </label>
                      <select
                        id="gender"
                        name="gender"
                        value={searchFilters.gender}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="any">Any</option>
                        <option value="male">Male Only</option>
                        <option value="female">Female Only</option>
                        <option value="mixed">Mixed Gender</option>
                      </select>
                    </div>
                    
                    <div>
                      <label htmlFor="furnished" className="block text-sm font-medium text-gray-700 mb-1">
                        Furnished
                      </label>
                      <select
                        id="furnished"
                        name="furnished"
                        value={searchFilters.furnished}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="any">Any</option>
                        <option value="yes">Furnished</option>
                        <option value="no">Unfurnished</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                {/* Price & Size */}
                <div className="border-t border-gray-200 pt-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Price & Size</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Price Range (TRY)
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="number"
                          name="priceMin"
                          placeholder="Min"
                          value={searchFilters.priceMin}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <input
                          type="number"
                          name="priceMax"
                          placeholder="Max"
                          value={searchFilters.priceMax}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Bedrooms & Bathrooms
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="number"
                          name="beds"
                          placeholder="Bedrooms"
                          value={searchFilters.beds}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                        <input
                          type="number"
                          name="baths"
                          placeholder="Bathrooms"
                          value={searchFilters.baths}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="area" className="block text-sm font-medium text-gray-700 mb-1">
                        Area (m²)
                      </label>
                      <input
                        type="number"
                        id="area"
                        name="area"
                        placeholder="Minimum area"
                        value={searchFilters.area}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
                
                {/* Amenities */}
                <div className="border-t border-gray-200 pt-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Amenities</h2>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
                    {amenitiesList.map((amenity) => (
                      <div key={amenity} className="flex items-center">
                        <input
                          id={`amenity-${amenity}`}
                          type="checkbox"
                          checked={searchFilters.amenities.includes(amenity)}
                          onChange={() => handleAmenityToggle(amenity)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`amenity-${amenity}`} className="ml-2 text-sm text-gray-700">
                          {amenity}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Additional Options */}
                <div className="border-t border-gray-200 pt-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Options</h2>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        id="availableNow"
                        name="availableNow"
                        type="checkbox"
                        checked={searchFilters.availableNow}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="availableNow" className="ml-2 text-sm text-gray-700">
                        Available Immediately
                      </label>
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        id="verified"
                        name="verified"
                        type="checkbox"
                        checked={searchFilters.verified}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="verified" className="ml-2 text-sm text-gray-700">
                        Verified Listings Only
                      </label>
                    </div>
                  </div>
                </div>
                
                {/* Search Button */}
                <div className="border-t border-gray-200 pt-6 flex justify-center">
                  <button
                    type="submit"
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Search
                  </button>
                </div>
              </div>
            </form>
          </div>
          
          {/* Search Results */}
          {hasSearched && (
            <div className="mt-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {results.length} Search Results
              </h2>
              
              {results.length === 0 ? (
                <div className="bg-white rounded-lg shadow-md p-8 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="mt-4 text-lg font-medium text-gray-900">No results found</h3>
                  <p className="mt-2 text-gray-600">
                    Try adjusting your filters or search criteria.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {results.map((listing) => (
                    <div key={listing.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                      <div className="h-48 relative">
                        <div 
                          className="absolute inset-0 bg-cover bg-center"
                          style={{ backgroundImage: `url(${listing.image})` }}
                        />
                        <div className="absolute top-2 right-2 bg-white text-xs font-bold px-2 py-1 rounded">
                          {listing.type}
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="font-bold text-gray-900 mb-1">{listing.title}</h3>
                        <p className="text-gray-600 text-sm">{listing.location}</p>
                        <p className="mt-2 text-blue-600 font-bold">
                          {listing.price.toLocaleString()} {listing.currency}/{listing.period}
                        </p>
                        <a 
                          href={`/listings/${listing.id}`}
                          className="mt-4 block text-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                        >
                          View Details
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}