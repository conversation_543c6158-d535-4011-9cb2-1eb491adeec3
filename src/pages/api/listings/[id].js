import { createRouter } from 'next-connect';
import dbConnect from '../../../utils/dbConnect';
import { authMiddleware } from '../../../utils/auth';
import Listing from '../../../models/Listing';
import upload from '../../../utils/multer';
import { updateListingImages, deleteListingImages } from '../../../utils/listingImages';

// Create a router instance
const router = createRouter();

// Middleware to run the auth middleware
const runMiddleware = (req, res, fn) => {
  return new Promise((resolve, reject) => {
    fn(req, res, (result) => {
      if (result instanceof Error) {
        return reject(result);
      }
      return resolve(result);
    });
  });
};

// Middleware to handle file upload (multiple images)
const handleUpload = upload.array('images', 10); // Max 10 images

// GET: Fetch a single listing by ID
router.get(async (req, res) => {
  try {
    await dbConnect();

    const { id } = req.query;

    // Find listing by ID and populate owner details
    const listing = await Listing.findById(id)
      .populate('owner', 'firstName lastName profileImage email phone');

    if (!listing) {
      return res.status(404).json({ success: false, message: 'Listing not found' });
    }

    // Increment view count
    listing.views += 1;
    await listing.save();

    res.status(200).json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error fetching listing:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// PUT: Update a listing
router.put(async (req, res) => {
  try {
    // Apply auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Handle file upload
    await new Promise((resolve, reject) => {
      handleUpload(req, res, (err) => {
        if (err) {
          console.error('Upload error:', err);
          return reject(err);
        }
        resolve();
      });
    });

    // Connect to database
    await dbConnect();

    const { id } = req.query;
    const userId = req.userId;

    // Find listing by ID
    const listing = await Listing.findById(id);

    if (!listing) {
      return res.status(404).json({ success: false, message: 'Listing not found' });
    }

    // Check if user is the owner of the listing
    if (listing.owner.toString() !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to update this listing' });
    }

    // Parse the listing data and image operations from the request body
    const listingData = JSON.parse(req.body.listingData || '{}');
    const imagesToDelete = JSON.parse(req.body.imagesToDelete || '[]');
    const mainImageUrl = req.body.mainImageUrl || '';

    // Update images if needed
    let updatedImages = listing.images;
    if (req.files?.length > 0 || imagesToDelete.length > 0 || mainImageUrl) {
      updatedImages = await updateListingImages(
        listing.images,
        req.files || [],
        imagesToDelete,
        mainImageUrl
      );
    }

    // Update listing with new data
    const updatedListing = await Listing.findByIdAndUpdate(
      id,
      {
        ...listingData,
        images: updatedImages,
      },
      { new: true, runValidators: true }
    );

    res.status(200).json({
      success: true,
      data: updatedListing,
    });
  } catch (error) {
    console.error('Error updating listing:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// DELETE: Delete a listing
router.delete(async (req, res) => {
  try {
    // Apply auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Connect to database
    await dbConnect();

    const { id } = req.query;
    const userId = req.userId;

    // Find listing by ID
    const listing = await Listing.findById(id);

    if (!listing) {
      return res.status(404).json({ success: false, message: 'Listing not found' });
    }

    // Check if user is the owner of the listing
    if (listing.owner.toString() !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to delete this listing' });
    }

    // Delete images from S3
    if (listing.images && listing.images.length > 0) {
      await deleteListingImages(listing.images);
    }

    // Delete the listing
    await Listing.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Listing deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting listing:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Export the router as the default handler
export default router.handler();

// Configure Next.js to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};
