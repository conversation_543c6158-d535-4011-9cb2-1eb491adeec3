import dbConnect from '../../../../utils/dbConnect';
import { authMiddleware } from '../../../../utils/auth';
import Listing from '../../../../models/Listing';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Create a middleware handler
  const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
      fn(req, res, (result) => {
        if (result instanceof Error) {
          return reject(result);
        }
        return resolve(result);
      });
    });
  };

  try {
    // Run the auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Connect to database
    await dbConnect();

    const { id } = req.query;
    const userId = req.userId;
    const { action } = req.body; // 'save' or 'unsave'

    // Find listing by ID
    const listing = await Listing.findById(id);

    if (!listing) {
      return res.status(404).json({ success: false, message: 'Listing not found' });
    }

    // Save or unsave the listing
    if (action === 'save') {
      // Check if already saved
      if (!listing.savedBy.includes(userId)) {
        listing.savedBy.push(userId);
        await listing.save();
      }
    } else if (action === 'unsave') {
      // Remove user from savedBy array
      listing.savedBy = listing.savedBy.filter(id => id.toString() !== userId);
      await listing.save();
    } else {
      return res.status(400).json({ success: false, message: 'Invalid action' });
    }

    res.status(200).json({
      success: true,
      message: action === 'save' ? 'Listing saved successfully' : 'Listing unsaved successfully',
      isSaved: action === 'save',
    });
  } catch (error) {
    console.error('Error saving/unsaving listing:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
