import mongoose from 'mongoose';
import dbConnect from '../../../utils/dbConnect';
import Listing from '../../../models/Listing';
import User from '../../../models/User';
import { createBoundingBox, calculateDistance as geoCalculateDistance } from '../../../utils/geoUtils';

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Connect to database
    await dbConnect();

    // Get query parameters
    const {
      q,                    // Text search query
      lat, lng, radius,     // Geolocation search
      userLat, userLng,     // User's current location for distance-based sorting
      priceMin, priceMax,   // Price range
      propertyType,         // Property type
      roomType,             // Room type
      bedrooms,             // Number of bedrooms
      bathrooms,            // Number of bathrooms
      amenities,            // Comma-separated list of amenities
      availableFrom,        // Available from date
      gender,               // Preferred gender
      furnished,            // Furnished status
      availableNow,         // Available now (boolean)
      verified,             // Verified listings only (boolean)
      city,                 // City filter
      neighborhood,         // Neighborhood filter
      limit = 20,           // Results per page
      skip = 0,             // Pagination offset
      sortBy = 'createdAt', // Sort field
      sortOrder = 'desc',   // Sort order
      commuteAddress,       // Address for commute time calculation
      commuteTime,          // Maximum commute time in minutes
      commuteMode           // Mode of transportation (driving, transit, walking, bicycling)
    } = req.query;

    // Build the query
    const query = {
      status: 'published'
    };

    // Text search
    if (q && q.trim()) {
      query.$text = { $search: q.trim() };
    }

    // Add verified filter if requested
    if (verified === 'true') {
      query.verified = true;
    }

    // Add available now filter if requested
    if (availableNow === 'true') {
      query['availability.availableFrom'] = { $lte: new Date() };
    }

    // Property type filter
    if (propertyType && propertyType !== 'all') {
      query.propertyType = propertyType;
    }

    // Room type filter
    if (roomType && roomType !== 'all') {
      query.roomType = roomType;
    }

    // Text search
    if (q) {
      query.$text = { $search: q };
    }
    
    // City filter
    if (city) {
      query['address.city'] = { $regex: new RegExp(city, 'i') };
    }
    
    // Neighborhood filter
    if (neighborhood) {
      query['address.neighborhood'] = { $regex: new RegExp(neighborhood, 'i') };
    }

    // Geolocation search
    if (lat && lng && radius) {
      // Create a separate query object for finding listings
      // We'll use this for the actual find() operation
      const findQuery = {
        ...query,
        'address.location': {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [parseFloat(lng), parseFloat(lat)]
            },
            $maxDistance: parseInt(radius) * 1000 // Convert km to meters
          }
        }
      };
      
      // For countDocuments, we need to use $geoWithin instead of $near
      query['address.location'] = {
        $geoWithin: {
          $centerSphere: [
            [parseFloat(lng), parseFloat(lat)],
            parseInt(radius) / 6371 // Convert radius to radians
          ]
        }
      };
      
      // Store the findQuery for later use
      req.findQuery = findQuery;
    }
    // User location proximity search (separate from explicit geolocation search)
    else if (userLat && userLng) {
      // Create a separate query object for finding listings
      const findQuery = {
        ...query
      };
      
      // Add location-based sorting for find operation
      findQuery['address.location'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(userLng), parseFloat(userLat)]
          },
          $maxDistance: 100 * 1000 // 100km radius by default
        }
      };
      
      // For countDocuments, we need to use $geoWithin instead of $near
      query['address.location'] = {
        $geoWithin: {
          $centerSphere: [
            [parseFloat(userLng), parseFloat(userLat)],
            100 / 6371 // 100km radius in radians
          ]
        }
      };
      
      // Store the findQuery for later use
      req.findQuery = findQuery;
      
      // Set default sort to distance if not specified
      if (!sortBy) {
        sortBy = 'distance';
        sort = {}; // Clear any existing sort
      }
    }

    // Price range filter (enhanced with validation)
    if (priceMin || priceMax) {
      query['price.amount'] = {};
      if (priceMin && !isNaN(priceMin)) {
        query['price.amount'].$gte = parseInt(priceMin);
      }
      if (priceMax && !isNaN(priceMax)) {
        query['price.amount'].$lte = parseInt(priceMax);
      }
    }

    // Bedrooms filter (enhanced)
    if (bedrooms && bedrooms !== 'any' && !isNaN(bedrooms)) {
      query['rooms.bedrooms'] = parseInt(bedrooms);
    }

    // Bathrooms filter (enhanced)
    if (bathrooms && bathrooms !== 'any' && !isNaN(bathrooms)) {
      query['rooms.bathrooms'] = parseInt(bathrooms);
    }

    // Amenities
    if (amenities) {
      const amenitiesList = amenities.split(',');
      amenitiesList.forEach(amenity => {
        query[`amenities.${amenity}`] = true;
      });
    }

    // Available from
    if (availableFrom) {
      query['availability.availableFrom'] = { $lte: new Date(availableFrom) };
    }

    // Preferred gender
    if (gender && gender !== 'any') {
      query['preferredTenants.gender'] = gender;
    }
    
    // Furnished status
    if (furnished && furnished !== 'any') {
      query['amenities.furnished'] = furnished === 'true' || furnished === 'yes';
    }

    // Additional filters for enhanced search
    if (req.query.petsAllowed === 'true') {
      query['amenities.petsAllowed'] = true;
    }

    if (req.query.smokingAllowed === 'true') {
      query['amenities.smokingAllowed'] = true;
    }

    // Tenant preferences
    if (req.query.students === 'true') {
      query['preferredTenants.students'] = true;
    }

    if (req.query.professionals === 'true') {
      query['preferredTenants.professionals'] = true;
    }

    if (req.query.couples === 'true') {
      query['preferredTenants.couples'] = true;
    }

    if (req.query.families === 'true') {
      query['preferredTenants.families'] = true;
    }

    // Create sort object
    const sort = {};
    
    // Handle special sort cases
    if (sortBy === 'price') {
      sort['price.amount'] = sortOrder === 'asc' ? 1 : -1;
    } else if (sortBy === 'featured') {
      // Sort by featured status first, then by creation date
      sort.isFeatured = -1;
      sort.createdAt = -1;
    } else if (sortBy === 'distance' && userLat && userLng) {
      // Distance-based sorting will be handled after query execution
      // We'll just use default sorting for the initial query
      sort.createdAt = -1;
    } else if (sortBy === 'commuteTime') {
      // Commute time sorting will be handled after query execution
      // We'll just use default sorting for the initial query
      sort.createdAt = -1;
    } else {
      // Default sort
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    }

    // Execute the query with pagination and sorting
    const findQuery = req.findQuery || query;
    
    // First ensure we have the models properly loaded
    // Make sure User model is initialized
    if (!mongoose.models.User) {
      // Force load the User model if it's not already loaded
      await import('../../../models/User');
    }
    
    try {
      // Execute the query with pagination and sorting
      const listings = await Listing.find(findQuery)
        .skip(parseInt(skip))
        .limit(parseInt(limit))
        .sort(sort)
        .populate('owner', 'firstName lastName profileImage')
        .lean();

    // Get total count for pagination
    const totalCount = await Listing.countDocuments(query);

    // Process listings for commute time if needed
    let processedListings = listings;
    
    // Add commute time estimation if requested
    // Note: For actual commute time calculation, you would need to use a service like Google Maps Distance Matrix API
    if (commuteAddress && commuteTime && commuteMode) {
      try {
        // In a real implementation, you would geocode the commuteAddress
        // For this example, we'll use a placeholder for Istanbul's center
        const destinationCoords = { lat: 41.0082, lng: 28.9784 };
        
        // Calculate estimated commute times based on distance and mode
        processedListings = processedListings.map(listing => {
          if (listing.address?.location?.coordinates) {
            const [lngListing, latListing] = listing.address.location.coordinates;
            
            // Calculate distance in kilometers
            const distanceKm = calculateDistance(
              destinationCoords.lat,
              destinationCoords.lng,
              latListing,
              lngListing
            );
            
            // Estimate commute time based on transportation mode
            // These are rough estimates and would be replaced with actual API calls
            const speeds = {
              driving: 30,    // km/h in urban areas
              transit: 20,    // km/h for public transit
              walking: 5,     // km/h walking speed
              bicycling: 15   // km/h cycling speed
            };
            
            const speed = speeds[commuteMode] || speeds.transit;
            const estimatedTimeHours = distanceKm / speed;
            const estimatedTimeMinutes = Math.round(estimatedTimeHours * 60);
            
            return {
              ...listing,
              commuteInfo: {
                distanceKm,
                estimatedTimeMinutes,
                formattedTime: `${estimatedTimeMinutes} min by ${commuteMode}`,
                mode: commuteMode
              }
            };
          }
          return listing;
        });
        
        // Filter listings by commute time if specified
        if (commuteTime) {
          const maxTimeMinutes = parseInt(commuteTime);
          processedListings = processedListings.filter(
            listing => listing.commuteInfo && listing.commuteInfo.estimatedTimeMinutes <= maxTimeMinutes
          );
        }
        
        // Sort by commute time if requested
        if (sortBy === 'commuteTime') {
          processedListings.sort((a, b) => {
            const timeA = a.commuteInfo?.estimatedTimeMinutes || Number.MAX_SAFE_INTEGER;
            const timeB = b.commuteInfo?.estimatedTimeMinutes || Number.MAX_SAFE_INTEGER;
            return timeA - timeB;
          });
        }
      } catch (error) {
        console.error('Error processing commute times:', error);
      }
    }

    // Add distance to each listing if user location is provided
    if (userLat && userLng) {
      // Check if user is in Pakistan (approximate coordinates check)
      const isPakistan = parseFloat(userLat) >= 23.0 && parseFloat(userLat) <= 37.0 && 
                        parseFloat(userLng) >= 60.0 && parseFloat(userLng) <= 78.0;
      
      processedListings = processedListings.map(listing => {
        // If listing has location coordinates, calculate distance
        if (listing.address && listing.address.location && listing.address.location.coordinates) {
          const [lngListing, latListing] = listing.address.location.coordinates;
          
          // Calculate distance in kilometers using the Haversine formula
          const distance = calculateDistance(
            parseFloat(userLat),
            parseFloat(userLng),
            latListing,
            lngListing
          );
          
          // For Pakistan locations, add a proximity flag
          let isNearby = false;
          if (isPakistan) {
            isNearby = distance <= 50; // Within 50km is considered nearby
          }
          
          return { ...listing, distance, isNearby };
        }
        return listing;
      });
      
      // Filter to show only nearby listings unless a specific city is requested
      if (!req.query.city && !req.query.q) {
        // Keep only listings within a reasonable distance (100km)
        processedListings = processedListings.filter(listing => 
          listing.distance !== undefined && listing.distance <= 100
        );
      }
      
      // Sort by distance if requested
      if (sortBy === 'distance') {
        // Check if user is in Pakistan
        const isPakistan = parseFloat(userLat) >= 23.0 && parseFloat(userLat) <= 37.0 && 
                          parseFloat(userLng) >= 60.0 && parseFloat(userLng) <= 78.0;
        
        if (isPakistan) {
          // For Pakistan locations, prioritize nearby listings first, then sort by distance
          processedListings.sort((a, b) => {
            // First prioritize nearby listings
            if (a.isNearby && !b.isNearby) return -1;
            if (!a.isNearby && b.isNearby) return 1;
            
            // Then sort by distance
            const distanceA = a.distance !== undefined ? a.distance : Number.MAX_SAFE_INTEGER;
            const distanceB = b.distance !== undefined ? b.distance : Number.MAX_SAFE_INTEGER;
            return distanceA - distanceB;
          });
        } else {
          // Regular distance sorting for other locations
          processedListings.sort((a, b) => {
            const distanceA = a.distance !== undefined ? a.distance : Number.MAX_SAFE_INTEGER;
            const distanceB = b.distance !== undefined ? b.distance : Number.MAX_SAFE_INTEGER;
            return distanceA - distanceB;
          });
        }
      }
    }

    return res.status(200).json({
      success: true,
      count: processedListings.length,
      totalCount,
      data: processedListings
    });
    } catch (populateError) {
      console.error('Error populating owner data:', populateError);
      // If populating fails, return listings without owner data
      const listings = await Listing.find(findQuery)
        .skip(parseInt(skip))
        .limit(parseInt(limit))
        .sort(sort)
        .lean();
        
      // Process the listings (same as before but without owner data)
      let processedListings = listings;
      
      // Get total count for pagination
      const totalCount = await Listing.countDocuments(query);
      
      return res.status(200).json({
        success: true,
        count: processedListings.length,
        totalCount,
        data: processedListings,
        note: 'Owner data could not be populated'
      });
    }
  } catch (error) {
    console.error('Search listings error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
}

/**
 * Calculate distance between two points using the Haversine formula
 * @param {Number} lat1 - Latitude of point 1
 * @param {Number} lon1 - Longitude of point 1
 * @param {Number} lat2 - Latitude of point 2
 * @param {Number} lon2 - Longitude of point 2
 * @returns {Number} - Distance in kilometers
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in km
  return Math.round(distance * 10) / 10; // Round to 1 decimal place
}

function deg2rad(deg) {
  return deg * (Math.PI / 180);
}
