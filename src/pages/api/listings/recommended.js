// pages/api/listings/recommended.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import Listing from '../../../models/Listing';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    const { limit = 6 } = req.query;

    // Get recommended listings (for now, just get recent active listings)
    const listings = await Listing.find({ 
      status: 'active',
      isVerified: true 
    })
    .populate('owner', 'firstName lastName profileImage')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit));

    // Transform the data for the frontend
    const recommendedProperties = listings.map(listing => ({
      id: listing._id,
      title: listing.title,
      location: `${listing.address.neighborhood}, ${listing.address.city}`,
      price: `$${listing.pricing.rent}/${listing.pricing.period}`,
      image: listing.images && listing.images.length > 0 ? listing.images[0] : null,
      type: listing.propertyType,
      beds: listing.details.bedrooms,
      baths: listing.details.bathrooms,
      area: listing.details.area
    }));

    res.status(200).json(recommendedProperties);

  } catch (error) {
    console.error('Error fetching recommended listings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
