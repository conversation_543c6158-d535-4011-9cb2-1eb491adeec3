import { createRouter } from 'next-connect';
import dbConnect from '../../../utils/dbConnect';
import { authMiddleware } from '../../../utils/auth';
import Listing from '../../../models/Listing';
import upload from '../../../utils/multer';
import { uploadListingImages } from '../../../utils/listingImages';

// Create a router instance
const router = createRouter();

// Middleware to run the auth middleware
const runMiddleware = (req, res, fn) => {
  return new Promise((resolve, reject) => {
    fn(req, res, (result) => {
      if (result instanceof Error) {
        return reject(result);
      }
      return resolve(result);
    });
  });
};

// Middleware to handle file upload (multiple images)
const handleUpload = upload.array('images', 10); // Max 10 images

// GET: Fetch all listings with filtering
router.get(async (req, res) => {
  try {
    await dbConnect();

    // Build query from request query parameters
    const query = buildQuery(req.query);

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Sorting
    const sort = {};
    if (req.query.sortBy) {
      const parts = req.query.sortBy.split(':');
      sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
    } else {
      // Default sort by createdAt in descending order
      sort.createdAt = -1;
    }

    // Execute query
    const listings = await Listing.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('owner', 'firstName lastName profileImage');

    // Get total count for pagination
    const total = await Listing.countDocuments(query);

    res.status(200).json({
      success: true,
      data: listings,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching listings:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// POST: Create a new listing
router.post(async (req, res) => {
  try {
    // Apply auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Handle file upload
    await new Promise((resolve, reject) => {
      handleUpload(req, res, (err) => {
        if (err) {
          console.error('Upload error:', err);
          return reject(err);
        }
        resolve();
      });
    });

    // Connect to database
    await dbConnect();

    // Get user ID from middleware
    const userId = req.userId;

    // Parse the listing data from the request body
    const listingData = JSON.parse(req.body.listingData || '{}');

    // Upload images if provided
    let images = [];
    if (req.files && req.files.length > 0) {
      images = await uploadListingImages(req.files);
    }

    // Create new listing
    const listing = await Listing.create({
      ...listingData,
      owner: userId,
      images,
    });

    // Return success response
    res.status(201).json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error creating listing:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
});

// Helper function to build query from request parameters
function buildQuery(queryParams) {
  const query = {};

  // Filter by property type
  if (queryParams.propertyType) {
    query.propertyType = queryParams.propertyType;
  }

  // Filter by room type
  if (queryParams.roomType) {
    query.roomType = queryParams.roomType;
  }

  // Filter by city
  if (queryParams.city) {
    query['address.city'] = { $regex: queryParams.city, $options: 'i' };
  }

  // Filter by country
  if (queryParams.country) {
    query['address.country'] = { $regex: queryParams.country, $options: 'i' };
  }

  // Filter by price range
  if (queryParams.minPrice || queryParams.maxPrice) {
    query.price = {};
    if (queryParams.minPrice) {
      query.price.$gte = parseInt(queryParams.minPrice);
    }
    if (queryParams.maxPrice) {
      query.price.$lte = parseInt(queryParams.maxPrice);
    }
  }

  // Filter by number of bedrooms
  if (queryParams.bedrooms) {
    query['rooms.bedrooms'] = parseInt(queryParams.bedrooms);
  }

  // Filter by number of bathrooms
  if (queryParams.bathrooms) {
    query['rooms.bathrooms'] = parseInt(queryParams.bathrooms);
  }

  // Filter by amenities
  const amenities = [
    'wifi', 'airConditioning', 'heating', 'tv', 'kitchen',
    'washer', 'dryer', 'parking', 'elevator', 'security',
    'gym', 'pool', 'furnished', 'petsAllowed', 'smokingAllowed'
  ];

  amenities.forEach(amenity => {
    if (queryParams[amenity] === 'true') {
      query[`amenities.${amenity}`] = true;
    }
  });

  // Filter by availability date
  if (queryParams.availableFrom) {
    query['availability.availableFrom'] = { $lte: new Date(queryParams.availableFrom) };
  }

  // Filter by status (default to published)
  query.status = queryParams.status || 'published';

  // Text search
  if (queryParams.search) {
    query.$text = { $search: queryParams.search };
  }

  // Filter by owner
  if (queryParams.owner) {
    query.owner = queryParams.owner;
  }

  return query;
}

// Export the router as the default handler
export default router.handler();

// Configure Next.js to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};
