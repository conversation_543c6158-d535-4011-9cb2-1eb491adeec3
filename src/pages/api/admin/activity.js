// pages/api/admin/activity.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;

    if (!userRole) {
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
    }

    // Check if user is admin
    if (userRole !== 'admin') {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }



    const { limit = 20 } = req.query;

    // For now, return mock activity data
    // In a real app, you would fetch from an activity log table
    const mockActivity = [
      {
        id: 1,
        description: 'New user registered: Sarah Johnson',
        timestamp: '5 minutes ago',
        type: 'user_registered',
        urgent: false
      },
      {
        id: 2,
        description: 'New listing created: Modern Studio in Manhattan',
        timestamp: '15 minutes ago',
        type: 'listing_created',
        urgent: false
      },
      {
        id: 3,
        description: 'Content reported: Inappropriate listing description',
        timestamp: '1 hour ago',
        type: 'report_submitted',
        urgent: true
      },
      {
        id: 4,
        description: 'Booking confirmed: Mike Chen - Brooklyn Apartment',
        timestamp: '2 hours ago',
        type: 'booking_made',
        urgent: false
      },
      {
        id: 5,
        description: 'User verification pending: Emily Davis',
        timestamp: '3 hours ago',
        type: 'verification_pending',
        urgent: true
      },
      {
        id: 6,
        description: 'New lister registered: Alex Rodriguez',
        timestamp: '4 hours ago',
        type: 'user_registered',
        urgent: false
      },
      {
        id: 7,
        description: 'Listing updated: Private Room in Queens',
        timestamp: '5 hours ago',
        type: 'listing_updated',
        urgent: false
      },
      {
        id: 8,
        description: 'Payment processed: $850 for Studio Rental',
        timestamp: '6 hours ago',
        type: 'payment_processed',
        urgent: false
      },
      {
        id: 9,
        description: 'User dispute reported: Booking cancellation issue',
        timestamp: '8 hours ago',
        type: 'dispute_reported',
        urgent: true
      },
      {
        id: 10,
        description: 'New listing created: Shared Apartment in Brooklyn',
        timestamp: '10 hours ago',
        type: 'listing_created',
        urgent: false
      }
    ];

    const limitedActivity = mockActivity.slice(0, parseInt(limit));
    res.status(200).json(limitedActivity);

  } catch (error) {
    console.error('Error fetching admin activity:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
