// pages/api/admin/stats.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';
import Listing from '../../../models/Listing';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;

    if (!userRole) {
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
    }

    // Check if user is admin
    if (userRole !== 'admin') {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }



    // Get actual counts from database
    const totalUsers = await User.countDocuments();
    const totalListings = await Listing.countDocuments();
    const activeListings = await Listing.countDocuments({ status: 'active' });
    
    // Get new users this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const newUsersThisMonth = await User.countDocuments({
      createdAt: { $gte: startOfMonth }
    });

    // Mock data for other stats (in real app, calculate from actual data)
    const mockStats = {
      totalUsers,
      totalListings,
      activeListings,
      newUsersThisMonth,
      totalBookings: 245,
      totalRevenue: 15750,
      pendingVerifications: 8,
      reportedContent: 3
    };

    res.status(200).json(mockStats);

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
