// pages/api/admin/health.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;

    if (!userRole) {
      await dbConnect();
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
    }

    // Check if user is admin
    if (userRole !== 'admin') {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }

    // Check database connection
    let dbStatus = 'healthy';
    try {
      await dbConnect();
    } catch (error) {
      dbStatus = 'error';
    }

    // Mock system health data
    const healthData = {
      status: dbStatus === 'healthy' ? 'healthy' : 'warning',
      uptime: '99.9%',
      responseTime: '120ms',
      database: dbStatus,
      lastChecked: new Date().toISOString()
    };

    res.status(200).json(healthData);

  } catch (error) {
    console.error('Error fetching system health:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
