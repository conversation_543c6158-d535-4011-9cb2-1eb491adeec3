import { authMiddleware } from '../../utils/auth';
import dbConnect from '../../utils/dbConnect';
import User from '../../models/User';

export default async function handler(req, res) {
  // Create a middleware handler
  const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
      fn(req, res, (result) => {
        if (result instanceof Error) {
          return reject(result);
        }
        return resolve(result);
      });
    });
  };

  try {
    // Run the auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Connect to database
    await dbConnect();

    // Get user ID from middleware
    const userId = req.userId;

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Return success message
    res.status(200).json({
      success: true,
      message: 'Authentication successful',
      user: {
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      },
    });
  } catch (error) {
    console.error('Authentication test error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
