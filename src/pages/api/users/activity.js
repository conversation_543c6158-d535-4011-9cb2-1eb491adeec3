// pages/api/users/activity.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // For now, return mock activity data
    // In a real app, you would fetch from an activity log table
    const mockActivity = [
      {
        id: 1,
        description: 'You saved a property in Manhattan',
        timestamp: '2 hours ago',
        type: 'property_saved'
      },
      {
        id: 2,
        description: 'You applied to a shared apartment',
        timestamp: '1 day ago',
        type: 'application_submitted'
      },
      {
        id: 3,
        description: 'You received a message from a property owner',
        timestamp: '2 days ago',
        type: 'message_received'
      },
      {
        id: 4,
        description: 'You updated your profile',
        timestamp: '3 days ago',
        type: 'profile_updated'
      },
      {
        id: 5,
        description: 'You joined Homieye',
        timestamp: '1 week ago',
        type: 'account_created'
      }
    ];

    res.status(200).json(mockActivity);

  } catch (error) {
    console.error('Error fetching user activity:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
