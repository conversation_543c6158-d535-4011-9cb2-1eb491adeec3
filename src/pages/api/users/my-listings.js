import dbConnect from '../../../utils/dbConnect';
import { authMiddleware } from '../../../utils/auth';
import Listing from '../../../models/Listing';

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Create a middleware handler
  const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
      fn(req, res, (result) => {
        if (result instanceof Error) {
          return reject(result);
        }
        return resolve(result);
      });
    });
  };

  try {
    // Run the auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Connect to database
    await dbConnect();

    const userId = req.userId;

    // Filter by status if provided
    const statusFilter = req.query.status ? { status: req.query.status } : {};

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Find listings owned by the user
    const listings = await Listing.find({ 
      owner: userId,
      ...statusFilter
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Get total count for pagination
    const total = await Listing.countDocuments({ 
      owner: userId,
      ...statusFilter
    });

    // Get counts by status for dashboard
    const statusCounts = await Listing.aggregate([
      { $match: { owner: userId } },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    // Format status counts
    const counts = {
      draft: 0,
      published: 0,
      rented: 0,
      archived: 0
    };

    statusCounts.forEach(item => {
      counts[item._id] = item.count;
    });

    res.status(200).json({
      success: true,
      data: listings,
      counts,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user listings:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
