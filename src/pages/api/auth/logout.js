export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // In a stateless JWT authentication system, we don't need to do anything server-side
  // The client will remove the token from localStorage
  
  res.status(200).json({ success: true, message: 'Logged out successfully' });
}
