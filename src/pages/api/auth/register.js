import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';
import { generateToken } from '../../../utils/auth';
import { sendVerificationEmail } from '../../../utils/emailService';

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Connect to database
    await dbConnect();

    const { firstName, lastName, email, password, accountType, role, authProvider = 'credentials' } = req.body;

    // Check if user already exists
    const userExists = await User.findOne({ email });

    if (userExists) {
      // If the user exists but doesn't have the credentials provider
      if (authProvider === 'credentials' && 
          userExists.authProviders && 
          !userExists.authProviders.includes('credentials')) {
        
        // Allow linking a credentials account to an existing social auth account
        userExists.firstName = firstName;
        userExists.lastName = lastName;
        userExists.password = password; // This will be hashed by the pre-save hook
        userExists.authProviders.push('credentials');
        
        await userExists.save();
        
        // Generate JWT token
        const token = generateToken(userExists._id);
        
        return res.status(200).json({
          success: true,
          data: {
            _id: userExists._id,
            firstName: userExists.firstName,
            lastName: userExists.lastName,
            email: userExists.email,
            accountType: userExists.accountType,
            isVerified: userExists.isVerified,
            authProviders: userExists.authProviders,
            token,
            message: 'Account linked successfully.',
          },
        });
      } else {
        return res.status(400).json({ 
          success: false, 
          message: 'An account with this email already exists. Try logging in instead.'
        });
      }
    }

    // Create new user with verification token
    const user = new User({
      firstName,
      lastName,
      email,
      password,
      accountType: accountType || (role === 'lister' ? 'landlord' : 'tenant'),
      authProviders: [authProvider],
      role: role || 'user', // Use provided role or default to 'user'
    });
    
    // Generate verification token
    const verificationToken = user.generateVerificationToken();
    
    // Save user to database
    await user.save();

    if (user) {
      // Send verification email
      try {
        await sendVerificationEmail(
          user.email,
          `${user.firstName} ${user.lastName}`,
          verificationToken
        );
      } catch (emailError) {
        console.error('Error sending verification email:', emailError);
        // Continue with registration even if email fails
      }

      // Generate JWT token
      const token = generateToken(user._id);

      // Return user data and token
      res.status(201).json({
        success: true,
        data: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          accountType: user.accountType,
          isVerified: user.isVerified,
          role: user.role,
          authProviders: user.authProviders,
          token,
          message: 'Registration successful. Please check your email to verify your account.',
        },
      });
    } else {
      res.status(400).json({ success: false, message: 'Invalid user data' });
    }
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
