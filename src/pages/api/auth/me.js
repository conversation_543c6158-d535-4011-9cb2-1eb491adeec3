import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';
import { authMiddleware } from '../../../utils/auth';

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  // Create a middleware handler
  const runMiddleware = (req, res, fn) => {
    return new Promise((resolve, reject) => {
      fn(req, res, (result) => {
        if (result instanceof Error) {
          return reject(result);
        }
        return resolve(result);
      });
    });
  };

  try {
    // Run the auth middleware
    await runMiddleware(req, res, authMiddleware);

    // Connect to database
    await dbConnect();

    // Get user ID from middleware
    const userId = req.userId;

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Return user data
    res.status(200).json({
      success: true,
      data: {
        _id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        accountType: user.accountType,
        isVerified: user.isVerified,
        profileImage: user.profileImage,
        bio: user.bio,
        phone: user.phone,
        address: user.address,
        city: user.city,
        country: user.country,
        occupation: user.occupation,
        preferences: user.preferences,
        socialLinks: user.socialLinks,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
