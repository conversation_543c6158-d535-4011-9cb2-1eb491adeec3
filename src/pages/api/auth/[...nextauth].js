import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';
import { generateToken } from '../../../utils/auth';
import { ObjectId } from 'mongodb';

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        await dbConnect();

        // Find user by email and include password for comparison
        const user = await User.findOne({ email: credentials.email }).select('+password');

        // Check if user exists and password matches
        if (!user || !(await user.matchPassword(credentials.password))) {
          return null;
        }

        // Generate JWT token
        const token = generateToken(user._id);

        return {
          id: user._id.toString(),
          name: `${user.firstName} ${user.lastName}`,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          image: user.profileImage || null,
          accountType: user.accountType,
          role: user.role,
          isVerified: user.isVerified,
          emailVerified: user.isVerified,
          token,
          authProviders: user.authProviders || []
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account, trigger, session }) {
      // Handle session update
      if (trigger === 'update' && session) {
        // Update the token with the latest user data
        if (session.user) {
          token.name = session.user.name || token.name;
          token.email = session.user.email || token.email;
          token.picture = session.user.image || token.picture;
          token.accountType = session.user.accountType || token.accountType;
        }
        return token;
      }

      // Initial sign in
      if (account && user) {
        await dbConnect();
        
        // For Google sign in
        if (account.provider === 'google') {
          // Check if user already exists
          let dbUser = await User.findOne({ email: user.email });
          
          if (!dbUser) {
            // Create new user from Google data
            const names = user.name.split(' ');
            const firstName = names[0];
            const lastName = names.length > 1 ? names.slice(1).join(' ') : '';
            
            dbUser = await User.create({
              firstName,
              lastName,
              email: user.email,
              password: Math.random().toString(36).slice(-10), // Random password
              isVerified: true, // Google accounts are pre-verified
              profileImage: user.image,
              authProviders: ['google']
            });
          } else {
            // Update existing user with Google provider if not already added
            if (!dbUser.authProviders || !dbUser.authProviders.includes('google')) {
              dbUser.authProviders = [...(dbUser.authProviders || []), 'google'];
              dbUser.isVerified = true; // Ensure the account is verified
              if (!dbUser.profileImage && user.image) {
                dbUser.profileImage = user.image;
              }
              await dbUser.save();
            }
          }
          
          // Add custom user data to token
          token.userId = dbUser._id.toString();
          token.firstName = dbUser.firstName;
          token.lastName = dbUser.lastName;
          token.accountType = dbUser.accountType;
          token.role = dbUser.role;
          token.isVerified = dbUser.isVerified;
          token.emailVerified = dbUser.isVerified;
          token.jwtToken = generateToken(dbUser._id);
          token.authProviders = dbUser.authProviders || [];
        } else {
          // For credentials sign in
          token.userId = user.id;
          token.firstName = user.firstName;
          token.lastName = user.lastName;
          token.accountType = user.accountType;
          token.role = user.role;
          token.isVerified = user.isVerified;
          token.emailVerified = user.emailVerified;
          token.jwtToken = user.token;
          token.authProviders = user.authProviders || [];
        }
      }
      
      return token;
    },
    async session({ session, token }) {
      // Send properties to the client
      session.user.id = token.userId;
      session.user.firstName = token.firstName;
      session.user.lastName = token.lastName;
      session.user.accountType = token.accountType;
      session.user.role = token.role;
      session.user.isVerified = token.isVerified;
      session.user.emailVerified = token.emailVerified;
      session.user.token = token.jwtToken;
      session.user.authProviders = token.authProviders || [];

      // Add profile data if available
      if (token.profileData) {
        session.user.profileData = token.profileData;
      }

      return session;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
});
