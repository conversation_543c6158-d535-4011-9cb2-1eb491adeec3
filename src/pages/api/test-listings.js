import dbConnect from '../../utils/dbConnect';
import Listing from '../../models/Listing';
import User from '../../models/User';

export default async function handler(req, res) {
  try {
    // Connect to database
    await dbConnect();

    // Get counts
    const listingCount = await Listing.countDocuments();
    const userCount = await User.countDocuments();

    // Get a sample listing if any exist
    const sampleListing = listingCount > 0 
      ? await Listing.findOne().populate('owner', 'firstName lastName email')
      : null;

    // Return success message
    res.status(200).json({
      success: true,
      message: 'Listings API is working!',
      stats: {
        listings: listingCount,
        users: userCount,
      },
      sampleListing,
    });
  } catch (error) {
    console.error('Test error:', error);
    res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}
