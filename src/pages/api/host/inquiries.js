// pages/api/host/inquiries.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;

    if (!userRole) {
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
    }

    // Check if user is lister or admin
    if (!['lister', 'admin'].includes(userRole)) {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }



    const { limit = 10 } = req.query;

    // For now, return mock inquiry data
    // In a real app, you would fetch from a messages/inquiries table
    const mockInquiries = [
      {
        id: 1,
        guestName: 'Jennifer Smith',
        message: 'Hi! I\'m interested in your studio apartment. Is it still available for January?',
        timestamp: '2 hours ago',
        unread: true
      },
      {
        id: 2,
        guestName: 'David Brown',
        message: 'Could you tell me more about the neighborhood and nearby amenities?',
        timestamp: '5 hours ago',
        unread: true
      },
      {
        id: 3,
        guestName: 'Maria Garcia',
        message: 'I would like to schedule a viewing for this weekend if possible.',
        timestamp: '1 day ago',
        unread: false
      },
      {
        id: 4,
        guestName: 'Tom Wilson',
        message: 'Is parking included with the rental? Also, are pets allowed?',
        timestamp: '2 days ago',
        unread: false
      },
      {
        id: 5,
        guestName: 'Anna Lee',
        message: 'Thank you for the quick response! I\'ll send my application today.',
        timestamp: '3 days ago',
        unread: false
      }
    ];

    const limitedInquiries = mockInquiries.slice(0, parseInt(limit));
    res.status(200).json(limitedInquiries);

  } catch (error) {
    console.error('Error fetching host inquiries:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
