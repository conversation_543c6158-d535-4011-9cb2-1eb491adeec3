// pages/api/host/stats.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';
import Listing from '../../../models/Listing';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;
    let userId = session.user.id;

    if (!userRole || !userId) {
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
      userId = user._id.toString();
    }

    // Check if user is lister or admin
    if (!['lister', 'admin'].includes(userRole)) {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }

    // Get user's listings count
    const totalListings = await Listing.countDocuments({
      owner: userId
    });

    const activeListings = await Listing.countDocuments({
      owner: userId,
      status: 'active'
    });

    // For now, return mock stats data
    // In a real app, you would calculate these from actual booking/application data
    const mockStats = {
      totalListings,
      activeListings,
      totalBookings: 15,
      monthlyRevenue: 3250,
      pendingApplications: 4,
      unreadMessages: 7
    };

    res.status(200).json(mockStats);

  } catch (error) {
    console.error('Error fetching host stats:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
