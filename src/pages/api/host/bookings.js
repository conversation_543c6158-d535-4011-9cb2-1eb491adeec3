// pages/api/host/bookings.js
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import dbConnect from '../../../utils/dbConnect';
import User from '../../../models/User';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ success: false, message: 'Unauthorized' });
    }

    await dbConnect();

    // If session doesn't have role, fetch user from database
    let userRole = session.user.role;

    if (!userRole) {
      // Fetch user from database using email
      const user = await User.findOne({ email: session.user.email });
      if (!user) {
        return res.status(401).json({ success: false, message: 'User not found' });
      }
      userRole = user.role;
    }

    // Check if user is lister or admin
    if (!['lister', 'admin'].includes(userRole)) {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }



    const { limit = 10 } = req.query;

    // For now, return mock booking data
    // In a real app, you would fetch from a bookings table
    const mockBookings = [
      {
        id: 1,
        guestName: 'Sarah Johnson',
        propertyTitle: 'Modern Studio in Manhattan',
        dates: 'Dec 15 - Dec 22, 2024',
        amount: 850,
        status: 'confirmed'
      },
      {
        id: 2,
        guestName: 'Mike Chen',
        propertyTitle: 'Shared Apartment in Brooklyn',
        dates: 'Dec 20 - Jan 5, 2025',
        amount: 1200,
        status: 'pending'
      },
      {
        id: 3,
        guestName: 'Emily Davis',
        propertyTitle: 'Private Room in Queens',
        dates: 'Jan 1 - Jan 15, 2025',
        amount: 650,
        status: 'confirmed'
      },
      {
        id: 4,
        guestName: 'Alex Rodriguez',
        propertyTitle: 'Studio Apartment',
        dates: 'Jan 10 - Jan 20, 2025',
        amount: 750,
        status: 'cancelled'
      },
      {
        id: 5,
        guestName: 'Lisa Wang',
        propertyTitle: 'Shared Apartment in Manhattan',
        dates: 'Jan 25 - Feb 10, 2025',
        amount: 1100,
        status: 'pending'
      }
    ];

    const limitedBookings = mockBookings.slice(0, parseInt(limit));
    res.status(200).json(limitedBookings);

  } catch (error) {
    console.error('Error fetching host bookings:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
}
