// pages/neighborhoods/index.js
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../../components/layout/Layout';

export default function NeighborhoodsIndex() {
  // Sample data for neighborhoods
  const neighborhoods = [
    {
      id: 'kadikoy',
      name: 'Kadıköy',
      description: 'Vibrant cultural hub on the Asian side with lively streets, cafes, and bars.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,kadikoy',
      area: 'Asian Side',
      averagePrice: '2,800 TRY/month',
      popularity: 'High',
      score: 4.8
    },
    {
      id: 'besiktas',
      name: '<PERSON><PERSON>ik<PERSON><PERSON>',
      description: 'Lively area with universities, restaurants, and nightlife, popular with students and young professionals.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,besiktas',
      area: 'European Side',
      averagePrice: '3,200 TRY/month',
      popularity: 'High',
      score: 4.7
    },
    {
      id: 'beyoglu',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Historic district with Istiklal Street, Galata Tower, and vibrant arts scene.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,beyoglu',
      area: 'European Side',
      averagePrice: '3,500 TRY/month',
      popularity: 'High',
      score: 4.6
    },
    {
      id: 'sisli',
      name: 'Şişli',
      description: 'Modern business and residential district with shopping malls and international companies.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,sisli',
      area: 'European Side',
      averagePrice: '3,100 TRY/month',
      popularity: 'Medium',
      score: 4.5
    },
    {
      id: 'uskudar',
      name: 'Üsküdar',
      description: 'Traditional area on the Asian side with beautiful mosques and Bosphorus views.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,uskudar',
      area: 'Asian Side',
      averagePrice: '2,600 TRY/month',
      popularity: 'Medium',
      score: 4.4
    },
    {
      id: 'fatih',
      name: 'Fatih',
      description: 'Historical heart of Istanbul with the Grand Bazaar, Hagia Sophia, and Blue Mosque.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,fatih',
      area: 'European Side',
      averagePrice: '2,900 TRY/month',
      popularity: 'Medium',
      score: 4.3
    },
    {
      id: 'bakirkoy',
      name: 'Bakırköy',
      description: 'Well-established residential area with shopping centers and seaside promenades.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,bakirkoy',
      area: 'European Side',
      averagePrice: '2,750 TRY/month',
      popularity: 'Medium',
      score: 4.2
    },
    {
      id: 'sariyer',
      name: 'Sarıyer',
      description: 'Beautiful area along the Bosphorus with forests, beaches, and upscale neighborhoods.',
      image: 'https://source.unsplash.com/random/800x600/?istanbul,sariyer',
      area: 'European Side',
      averagePrice: '3,800 TRY/month',
      popularity: 'Medium',
      score: 4.5
    }
  ];

  // Group neighborhoods by area
  const groupedNeighborhoods = neighborhoods.reduce((acc, neighborhood) => {
    if (!acc[neighborhood.area]) {
      acc[neighborhood.area] = [];
    }
    acc[neighborhood.area].push(neighborhood);
    return acc;
  }, {});

  return (
    <Layout>
      <Head>
        <title>Istanbul Neighborhoods Guide | Istanbul Rooms</title>
        <meta name="description" content="Explore Istanbul's vibrant neighborhoods and find the perfect area for your next home." />
      </Head>
      
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Istanbul Neighborhoods Guide</h1>
          <p className="text-xl max-w-3xl mx-auto">
            Discover the unique character of Istanbul's diverse neighborhoods and find your perfect match
          </p>
        </div>
      </div>
      
      <div className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Intro Text */}
          <div className="max-w-3xl mx-auto text-center mb-16">
            <p className="text-lg text-gray-700">
              Istanbul is a city of contrasts, spanning two continents with neighborhoods that range from historic to ultra-modern. 
              Explore each area's unique character to find the one that matches your lifestyle and preferences.
            </p>
          </div>
          
          {/* Map Overview Section */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Istanbul at a Glance</h2>
            
            <div className="aspect-video bg-gray-200 rounded-lg mb-6">
              {/* In a real app, you would use an interactive map here */}
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-500">Interactive city map would be displayed here</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-bold text-blue-800">European Side</h3>
                <p className="text-gray-600 text-sm mt-1">Historic center, business districts, and vibrant nightlife</p>
              </div>
              <div className="bg-indigo-50 p-4 rounded-lg">
                <h3 className="font-bold text-indigo-800">Asian Side</h3>
                <p className="text-gray-600 text-sm mt-1">Relaxed atmosphere with trendy neighborhoods and parks</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-bold text-green-800">Bosphorus Areas</h3>
                <p className="text-gray-600 text-sm mt-1">Scenic waterfront neighborhoods with stunning views</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-bold text-purple-800">Suburban Districts</h3>
                <p className="text-gray-600 text-sm mt-1">Quieter residential areas further from the center</p>
              </div>
            </div>
          </div>
          
          {/* Neighborhoods by Area */}
          {Object.entries(groupedNeighborhoods).map(([area, areaNeighborhoods]) => (
            <div key={area} className="mb-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <span 
                  className={`w-3 h-3 rounded-full mr-2 ${
                    area === 'European Side' ? 'bg-blue-600' : 'bg-indigo-600'
                  }`}
                ></span>
                {area}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {areaNeighborhoods.map((neighborhood) => (
                  <Link 
                    key={neighborhood.id}
                    href={`/neighborhoods/${neighborhood.id}`}
                  >
                    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                      <div className="h-48 relative">
                        <div 
                          className="absolute inset-0 bg-cover bg-center"
                          style={{ backgroundImage: `url(${neighborhood.image})` }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent opacity-60"></div>
                        <div className="absolute bottom-0 left-0 p-4">
                          <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                        </div>
                      </div>
                      
                      <div className="p-4">
                        <p className="text-gray-600 text-sm mb-4">{neighborhood.description}</p>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-gray-500">Avg. Price</p>
                            <p className="font-medium">{neighborhood.averagePrice}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Popularity</p>
                            <p className="font-medium">{neighborhood.popularity}</p>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex items-center">
                          <div className="flex-1">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <svg 
                                  key={i}
                                  className={`h-4 w-4 ${i < Math.floor(neighborhood.score) ? 'text-yellow-400' : 'text-gray-300'}`}
                                  xmlns="http://www.w3.org/2000/svg" 
                                  viewBox="0 0 20 20" 
                                  fill="currentColor"
                                >
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                              <span className="ml-1 text-sm text-gray-600">{neighborhood.score}</span>
                            </div>
                          </div>
                          
                          <span className="inline-flex items-center text-blue-600 font-medium">
                            Explore
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
          
          {/* Neighborhood Comparison */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Neighborhood Comparison</h2>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Neighborhood
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Avg. Price (Room)
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Commute to Center
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      International Community
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nightlife
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Safety
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {neighborhoods.map((neighborhood) => (
                    <tr key={neighborhood.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link 
                          href={`/neighborhoods/${neighborhood.id}`}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {neighborhood.name}
                        </Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {neighborhood.averagePrice}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {/* Simulated commute times */}
                        {neighborhood.area === 'Asian Side' ? '30-45 min' : '15-25 min'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {/* Simulated international community rating */}
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => {
                            const score = neighborhood.name === 'Kadıköy' || neighborhood.name === 'Beşiktaş' || neighborhood.name === 'Beyoğlu' ? 5 : 
                                       neighborhood.name === 'Şişli' || neighborhood.name === 'Sarıyer' ? 4 : 3;
                            return (
                              <svg 
                                key={i}
                                className={`h-4 w-4 ${i < score ? 'text-blue-400' : 'text-gray-300'}`}
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 20 20" 
                                fill="currentColor"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            );
                          })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {/* Simulated nightlife rating */}
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => {
                            const score = neighborhood.name === 'Beyoğlu' ? 5 : 
                                       neighborhood.name === 'Kadıköy' || neighborhood.name === 'Beşiktaş' ? 4 : 
                                       neighborhood.name === 'Şişli' ? 3 : 2;
                            return (
                              <svg 
                                key={i}
                                className={`h-4 w-4 ${i < score ? 'text-purple-400' : 'text-gray-300'}`}
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 20 20" 
                                fill="currentColor"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            );
                          })}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {/* Simulated safety rating */}
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => {
                            const score = neighborhood.name === 'Üsküdar' || neighborhood.name === 'Sarıyer' || neighborhood.name === 'Bakırköy' ? 5 : 
                                       neighborhood.name === 'Kadıköy' || neighborhood.name === 'Beşiktaş' ? 4 : 
                                       neighborhood.name === 'Şişli' || neighborhood.name === 'Fatih' ? 3 : 4;
                            return (
                              <svg 
                                key={i}
                                className={`h-4 w-4 ${i < score ? 'text-green-400' : 'text-gray-300'}`}
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 20 20" 
                                fill="currentColor"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            );
                          })}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Neighborhood Quiz */}
          <div className="bg-blue-50 rounded-lg shadow-md p-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Not sure which neighborhood is right for you?</h2>
              <p className="text-gray-600 mb-6">
                Take our quick questionnaire to find the perfect match for your lifestyle and preferences.
              </p>
              
              <Link href="/neighborhood-quiz" className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
                Take the Neighborhood Quiz
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}