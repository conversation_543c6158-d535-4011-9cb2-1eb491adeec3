// pages/unauthorized.jsx
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import Layout from '../components/layout/Layout';

const UnauthorizedPage = () => {
  const { data: session } = useSession();

  const getDashboardLink = () => {
    if (!session) return '/auth/login';
    
    switch (session.user.role) {
      case 'admin':
        return '/admin';
      case 'lister':
        return '/dashboard/host';
      default:
        return '/dashboard';
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100">
              <svg className="h-12 w-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Access Denied
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              You don't have permission to access this page.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-8">
            <div className="text-center space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Insufficient Permissions
              </h3>
              <p className="text-sm text-gray-600">
                The page you're trying to access requires different permissions than your current account has.
              </p>
              
              {session ? (
                <div className="space-y-3">
                  <p className="text-xs text-gray-500">
                    Current role: <span className="font-medium capitalize">{session.user.role}</span>
                  </p>
                  <div className="space-y-2">
                    <Link href={getDashboardLink()}>
                      <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors duration-200">
                        Go to My Dashboard
                      </button>
                    </Link>
                    <Link href="/">
                      <button className="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200">
                        Return to Homepage
                      </button>
                    </Link>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Link href="/auth/login">
                    <button className="w-full bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors duration-200">
                      Sign In
                    </button>
                  </Link>
                  <Link href="/">
                    <button className="w-full border border-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-50 transition-colors duration-200">
                      Return to Homepage
                    </button>
                  </Link>
                </div>
              )}
            </div>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Need help?{' '}
              <Link href="/contact" className="font-medium text-blue-600 hover:text-blue-500">
                Contact support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default UnauthorizedPage;
