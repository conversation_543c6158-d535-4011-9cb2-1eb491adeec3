// pages/dashboard/listings.js
import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import MyListings from '../../components/dashboard/MyListings';
import ProtectedRoute from '../../components/auth/ProtectedRoute';
import { getMyListings } from '../../utils/listingService';

export default function ListingsPage() {
  const router = useRouter();
  const [userListings, setUserListings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('published');

  // Check for success message from query params
  useEffect(() => {
    const { created, updated, deleted } = router.query;
    if (created === 'true') {
      // Show success message for created listing
      console.log('Listing created successfully!');
    } else if (updated === 'true') {
      // Show success message for updated listing
      console.log('Listing updated successfully!');
    } else if (deleted === 'true') {
      // Show success message for deleted listing
      console.log('Listing deleted successfully!');
    }
  }, [router.query]);

  // Fetch user's listings from API
  useEffect(() => {
    const fetchListings = async () => {
      setIsLoading(true);
      setError('');

      try {
        const result = await getMyListings(activeTab);

        if (result.success) {
          setUserListings(result.data || []);
        } else {
          setError(result.message || 'Failed to load listings');
          setUserListings([]);
        }
      } catch (err) {
        console.error('Error fetching listings:', err);
        setError('An error occurred while loading your listings');
        setUserListings([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchListings();
  }, [activeTab]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <ProtectedRoute>
      <Layout>
        <Head>
          <title>My Listings | Istanbul Rooms</title>
          <meta name="description" content="Manage your property listings" />
        </Head>

        <div className="bg-gray-100 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row gap-8">
              {/* Sidebar */}
              <div className="w-full md:w-1/4">
                <DashboardNav />

              {/* Listing Tips */}
              <div className="mt-6 bg-blue-50 rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Listing Tips</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Use high-quality photos to showcase your property
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Write detailed descriptions with accurate information
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Keep your listing updated and respond to inquiries promptly
                  </li>
                </ul>
                <a href="/listing-guide" className="mt-3 text-blue-600 hover:text-blue-800 text-sm inline-flex items-center">
                  Read our full listing guide
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Main Content */}
            <div className="w-full md:w-3/4">
              {error && (
                <div className="mb-4 bg-red-50 border-l-4 border-red-500 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {isLoading ? (
                <div className="bg-white rounded-lg shadow-md p-6 flex justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <MyListings
                  listings={userListings}
                  activeTab={activeTab}
                  onTabChange={handleTabChange}
                />
              )}

              {/* Pro Upgrade Banner */}
              <div className="mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg shadow-md p-6 text-white">
                <div className="md:flex md:items-center md:justify-between">
                  <div>
                    <h2 className="text-xl font-bold">Upgrade to Pro Lister</h2>
                    <p className="mt-1 text-purple-100">
                      Get more visibility, featured listings, and unlimited photos.
                    </p>
                  </div>
                  <div className="mt-4 md:mt-0">
                    <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-indigo-600 bg-white hover:bg-indigo-50">
                      Upgrade Now
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
    </ProtectedRoute>
  );
}