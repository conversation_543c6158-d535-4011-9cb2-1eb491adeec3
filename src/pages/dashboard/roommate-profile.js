// pages/dashboard/roommate-profile.js
import { useState, useEffect } from 'react';
import Head from 'next/head';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import { dummyRoommates } from '../../utils/dummyData';

export default function RoommateProfile() {
  // For demo purposes, use the first dummy roommate as the user's profile
  const [profileData, setProfileData] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // In a real app, you would fetch the user's roommate profile data
  useEffect(() => {
    // Simulate API call to get user's roommate profile
    setTimeout(() => {
      // Use the first dummy roommate as the user's profile
      setProfileData({
        ...dummyRoommates[0],
        // Override some properties to make it more like a user profile
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+90 ************',
        bio: 'Tech professional working remotely for a US company. I\'m clean, quiet, and respectful. I enjoy cooking, hiking, and occasional social gatherings. Looking for a comfortable place with like-minded roommates.',
        isPublic: true
      });
      setIsLoading(false);
    }, 500);
  }, []);

  const handleSaveProfile = (e) => {
    e.preventDefault();
    // In a real app, you would save the profile data to the backend
    console.log('Saving profile:', profileData);
    setIsEditing(false);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleToggleEdit = () => {
    setIsEditing(!isEditing);
  };

  return (
    <Layout>
      <Head>
        <title>Roommate Profile | Istanbul Rooms</title>
        <meta name="description" content="Manage your roommate profile, preferences, and visibility settings" />
      </Head>
      
      <div className="bg-gray-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full md:w-1/4">
              <DashboardNav />
              
              {/* Profile Visibility Card */}
              {profileData && (
                <div className="mt-6 bg-white rounded-lg shadow-md p-4">
                  <div className="text-center">
                    <h3 className="font-medium text-gray-900 mb-1">Profile Visibility</h3>
                    <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {profileData.isPublic ? 'Public' : 'Private'}
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Profile Completeness</span>
                      <span className="font-medium text-gray-900">85%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                    
                    <div className="mt-4 flex justify-between text-sm">
                      <span className="text-gray-600">Verification</span>
                      <span className="font-medium text-gray-900">{profileData.verified ? 'Verified' : 'Not Verified'}</span>
                    </div>
                    
                    {!profileData.verified && (
                      <button className="mt-2 w-full py-1 px-3 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded">
                        Verify Account
                      </button>
                    )}
                  </div>
                </div>
              )}
              
              {/* Tips Card */}
              <div className="mt-6 bg-blue-50 rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Profile Tips</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Add a clear profile photo to increase trust
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Be specific about your preferences and lifestyle
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verify your account to get more responses
                  </li>
                </ul>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="w-full md:w-3/4">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h2 className="text-lg font-bold text-gray-900">Your Roommate Profile</h2>
                    <button
                      onClick={handleToggleEdit}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      {isEditing ? 'Cancel' : 'Edit Profile'}
                    </button>
                  </div>
                </div>
                
                {isLoading ? (
                  <div className="p-6 text-center">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-2"></div>
                    <p className="text-gray-600">Loading your profile...</p>
                  </div>
                ) : profileData ? (
                  <div className="p-6">
                    {isEditing ? (
                      <form onSubmit={handleSaveProfile}>
                        <div className="space-y-6">
                          {/* Basic Information */}
                          <div>
                            <h3 className="text-md font-medium text-gray-900 mb-4">Basic Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                                  Full Name
                                </label>
                                <input
                                  type="text"
                                  id="name"
                                  name="name"
                                  value={profileData.name}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                              <div>
                                <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">
                                  Age
                                </label>
                                <input
                                  type="number"
                                  id="age"
                                  name="age"
                                  value={profileData.age}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  min="18"
                                  max="99"
                                />
                              </div>
                              <div>
                                <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
                                  Gender
                                </label>
                                <select
                                  id="gender"
                                  name="gender"
                                  value={profileData.gender}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                >
                                  <option value="Male">Male</option>
                                  <option value="Female">Female</option>
                                  <option value="Other">Other</option>
                                  <option value="Prefer not to say">Prefer not to say</option>
                                </select>
                              </div>
                              <div>
                                <label htmlFor="occupation" className="block text-sm font-medium text-gray-700 mb-1">
                                  Occupation
                                </label>
                                <input
                                  type="text"
                                  id="occupation"
                                  name="occupation"
                                  value={profileData.occupation}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                          
                          {/* Contact Information */}
                          <div>
                            <h3 className="text-md font-medium text-gray-900 mb-4">Contact Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                  Email
                                </label>
                                <input
                                  type="email"
                                  id="email"
                                  name="email"
                                  value={profileData.email}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                              <div>
                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                  Phone
                                </label>
                                <input
                                  type="tel"
                                  id="phone"
                                  name="phone"
                                  value={profileData.phone}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                          
                          {/* Housing Preferences */}
                          <div>
                            <h3 className="text-md font-medium text-gray-900 mb-4">Housing Preferences</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                                  Preferred Locations
                                </label>
                                <input
                                  type="text"
                                  id="location"
                                  name="location"
                                  value={profileData.location}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="e.g. Beşiktaş, Şişli"
                                />
                              </div>
                              <div>
                                <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                                  Budget Range
                                </label>
                                <input
                                  type="text"
                                  id="budget"
                                  name="budget"
                                  value={profileData.budget}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="e.g. 2000-3000 TRY"
                                />
                              </div>
                              <div>
                                <label htmlFor="moveInDate" className="block text-sm font-medium text-gray-700 mb-1">
                                  Move-in Date
                                </label>
                                <input
                                  type="text"
                                  id="moveInDate"
                                  name="moveInDate"
                                  value={profileData.moveInDate}
                                  onChange={handleInputChange}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="e.g. June 1, 2025"
                                />
                              </div>
                            </div>
                          </div>
                          
                          {/* Lifestyle */}
                          <div>
                            <h3 className="text-md font-medium text-gray-900 mb-4">Lifestyle</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    name="smoking"
                                    checked={profileData.smoking}
                                    onChange={(e) => setProfileData({...profileData, smoking: e.target.checked})}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                  <span className="ml-2 text-sm text-gray-700">Smoker</span>
                                </label>
                              </div>
                              <div>
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    name="pets"
                                    checked={profileData.pets}
                                    onChange={(e) => setProfileData({...profileData, pets: e.target.checked})}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  />
                                  <span className="ml-2 text-sm text-gray-700">Has Pets</span>
                                </label>
                              </div>
                            </div>
                          </div>
                          
                          {/* Bio */}
                          <div>
                            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                              About Me
                            </label>
                            <textarea
                              id="bio"
                              name="bio"
                              rows={4}
                              value={profileData.bio}
                              onChange={handleInputChange}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Tell potential roommates about yourself, your lifestyle, and what you're looking for"
                            ></textarea>
                          </div>
                          
                          {/* Profile Visibility */}
                          <div>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                name="isPublic"
                                checked={profileData.isPublic}
                                onChange={(e) => setProfileData({...profileData, isPublic: e.target.checked})}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">Make my profile public and visible to other users</span>
                            </label>
                          </div>
                          
                          {/* Submit Button */}
                          <div className="flex justify-end">
                            <button
                              type="submit"
                              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                              Save Changes
                            </button>
                          </div>
                        </div>
                      </form>
                    ) : (
                      <div className="space-y-6">
                        {/* Profile Header */}
                        <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
                          <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-200">
                            <img 
                              src={profileData.image} 
                              alt={profileData.name} 
                              className="h-full w-full object-cover"
                            />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900">{profileData.name}, {profileData.age}</h3>
                            <p className="text-gray-600">{profileData.occupation}</p>
                            
                            <div className="mt-2 flex flex-wrap gap-2">
                              {profileData.verified && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  Verified
                                </span>
                              )}
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {profileData.gender}
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                Budget: {profileData.budget}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        {/* About Me */}
                        <div>
                          <h3 className="text-md font-medium text-gray-900 mb-2">About Me</h3>
                          <p className="text-gray-700">{profileData.bio}</p>
                        </div>
                        
                        {/* Housing Preferences */}
                        <div>
                          <h3 className="text-md font-medium text-gray-900 mb-2">Housing Preferences</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500">Preferred Locations</p>
                              <p className="text-gray-700">{profileData.location}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Budget Range</p>
                              <p className="text-gray-700">{profileData.budget}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Move-in Date</p>
                              <p className="text-gray-700">{profileData.moveInDate}</p>
                            </div>
                          </div>
                        </div>
                        
                        {/* Lifestyle */}
                        <div>
                          <h3 className="text-md font-medium text-gray-900 mb-2">Lifestyle</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-500">Smoking</p>
                              <p className="text-gray-700">{profileData.smoking ? 'Smoker' : 'Non-smoker'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-500">Pets</p>
                              <p className="text-gray-700">{profileData.pets ? 'Has pets' : 'No pets'}</p>
                            </div>
                          </div>
                        </div>
                        
                        {/* Interests */}
                        <div>
                          <h3 className="text-md font-medium text-gray-900 mb-2">Interests</h3>
                          <div className="flex flex-wrap gap-2">
                            {profileData.interests && profileData.interests.map((interest, index) => (
                              <span 
                                key={index}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                              >
                                {interest}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        {/* Languages */}
                        <div>
                          <h3 className="text-md font-medium text-gray-900 mb-2">Languages</h3>
                          <div className="flex flex-wrap gap-2">
                            {profileData.languages && profileData.languages.map((language, index) => (
                              <span 
                                key={index}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                              >
                                {language}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        {/* Profile Visibility */}
                        <div className="pt-4 border-t border-gray-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-900">Profile Visibility</p>
                              <p className="text-sm text-gray-500">
                                {profileData.isPublic 
                                  ? 'Your profile is public and visible to other users' 
                                  : 'Your profile is private and not visible to other users'}
                              </p>
                            </div>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              profileData.isPublic 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {profileData.isPublic ? 'Public' : 'Private'}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="p-6 text-center">
                    <p className="text-gray-600 mb-4">You haven't created a roommate profile yet.</p>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Create Profile
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
