// pages/dashboard/messages.js
import { useState, useEffect } from 'react';
import Head from 'next/head';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import Messages from '../../components/dashboard/Messages';

export default function MessagesPage() {
  const [conversations, setConversations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // In a real app, you would fetch user's conversations from an API
  useEffect(() => {
    // Simulate API call to get conversations
    const loadDummyData = () => {
      // Create dummy conversations
      const dummyConversations = [
        {
          id: '1',
          otherUser: {
            id: 'user1',
            name: '<PERSON><PERSON>',
            image: 'https://source.unsplash.com/random/100x100/?portrait,woman,1'
          },
          listingTitle: 'Modern Apartment in Kadıköy',
          lastMessage: 'Is the room still available?',
          lastMessageTime: '10:42 AM',
          unreadCount: 1,
          messages: [
            {
              id: 'm1',
              text: 'Hi, I saw your listing for the apartment in Kadıköy. Is it still available?',
              time: 'Yesterday, 8:30 PM',
              fromSelf: false
            },
            {
              id: 'm2',
              text: 'Yes, it is! Would you like to schedule a viewing?',
              time: 'Yesterday, 9:15 PM',
              fromSelf: true
            },
            {
              id: 'm3',
              text: 'That would be great! How about tomorrow afternoon?',
              time: 'Yesterday, 9:20 PM',
              fromSelf: false
            },
            {
              id: 'm4',
              text: 'Sure, I can do 3 PM. Does that work for you?',
              time: 'Yesterday, 9:25 PM',
              fromSelf: true
            },
            {
              id: 'm5',
              text: 'Is the room still available?',
              time: '10:42 AM',
              fromSelf: false
            }
          ]
        },
        {
          id: '2',
          otherUser: {
            id: 'user2',
            name: 'Ahmet Kaya',
            image: 'https://source.unsplash.com/random/100x100/?portrait,man,1'
          },
          listingTitle: 'Cozy Room in Beşiktaş',
          lastMessage: `I'll be there at 5 PM today.`,
          lastMessageTime: 'Yesterday',
          unreadCount: 0,
          messages: [
            {
              id: 'm1',
              text: `Hello, I'm interested in your room listing in Beşiktaş.`,
              time: '2 days ago, 2:15 PM',
              fromSelf: false
            },
            {
              id: 'm2',
              text: 'Hi Ahmet! The room is still available. Would you like to see it?',
              time: '2 days ago, 3:30 PM',
              fromSelf: true
            },
            {
              id: 'm3',
              text: 'Yes, I would love to! When would be a good time?',
              time: '2 days ago, 4:00 PM',
              fromSelf: false
            },
            {
              id: 'm4',
              text: 'How about tomorrow at 5 PM?',
              time: '2 days ago, 4:15 PM',
              fromSelf: true
            },
            {
              id: 'm5',
              text: 'Ill be there at 5 PM today.',
              time: 'Yesterday, 10:30 AM',
              fromSelf: false
            }
          ]
        },
        {
          id: '3',
          otherUser: {
            id: 'user3',
            name: 'Sophia Martinez',
            image: 'https://source.unsplash.com/random/100x100/?portrait,woman,2'
          },
          listingTitle: null, // This could be a direct message
          lastMessage: 'Looking forward to being roommates!',
          lastMessageTime: 'Tuesday',
          unreadCount: 0,
          messages: [
            {
              id: 'm1',
              text: 'Hi! I saw your profile and we seem to have similar preferences for apartments.',
              time: 'Tuesday, 11:20 AM',
              fromSelf: false
            },
            {
              id: 'm2',
              text: 'Hello Sophia! Yes, I also noticed that. Are you looking for a place in Kadıköy?',
              time: 'Tuesday, 12:05 PM',
              fromSelf: true
            },
            {
              id: 'm3',
              text: 'Yes, I am! Would you be interested in searching together? Maybe we could be roommates.',
              time: 'Tuesday, 12:30 PM',
              fromSelf: false
            },
            {
              id: 'm4',
              text: 'That sounds like a great idea! Should we meet for coffee to discuss?',
              time: 'Tuesday, 1:45 PM',
              fromSelf: true
            },
            {
              id: 'm5',
              text: 'Looking forward to being roommates!',
              time: 'Tuesday, 2:20 PM',
              fromSelf: false
            }
          ]
        }
      ];
      
      setConversations(dummyConversations);
      setIsLoading(false);
    };
    
    // Simulate network delay
    setTimeout(loadDummyData, 800);
  }, []);

  return (
    <Layout>
      <Head>
        <title>Messages | Istanbul Rooms</title>
        <meta name="description" content="Manage your messages and conversations" />
      </Head>
      
      <div className="bg-gray-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full md:w-1/4">
              <DashboardNav />
              
              {/* Quick Tips */}
              <div className="mt-6 bg-blue-50 rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Messaging Tips</h3>
                <ul className="text-sm text-gray-600 space-y-2">
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Respond promptly to increase your chances
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Be clear about your expectations and questions
                  </li>
                  <li className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Always meet in public places for the first time
                  </li>
                </ul>
                <a href="/safety" className="mt-3 text-blue-600 hover:text-blue-800 text-sm inline-flex items-center">
                  Read our safety guide
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="w-full md:w-3/4">
              {isLoading ? (
                <div className="bg-white rounded-lg shadow-md p-6 flex justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <Messages conversations={conversations} />
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}