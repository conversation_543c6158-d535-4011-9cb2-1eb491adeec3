// pages/dashboard/settings.js
import { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import ProtectedRoute from '../../components/auth/ProtectedRoute';
import { useAuth } from '../../utils/authContext';

export default function SettingsPage() {
  const router = useRouter();
  const { user, getProfile, updateProfile, uploadProfileImage } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const fileInputRef = useRef(null);

  // User Profile State
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    photo: null,
    photoUrl: '',
    bio: '',
    languages: ['English']
  });

  // Notification Settings State
  const [notificationSettings, setNotificationSettings] = useState({
    emailNewMessages: true,
    emailListingUpdates: true,
    emailNewsletters: false,
    smsNewMessages: false,
    smsListingUpdates: false
  });

  // Account Settings State
  const [accountSettings, setAccountSettings] = useState({
    currency: 'TRY',
    timeZone: 'Europe/Istanbul',
    language: 'English'
  });

  // Privacy Settings State
  const [privacySettings, setPrivacySettings] = useState({
    showContactInfo: false,
    showSocialMedia: false,
    allowLocationAccess: true
  });

  // Password Change State
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Active Tab State
  const [activeTab, setActiveTab] = useState('profile');

  // Handle changes to different settings forms
  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleNotificationChange = (e) => {
    const { name, checked } = e.target;
    setNotificationSettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleAccountChange = (e) => {
    const { name, value } = e.target;
    setAccountSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePrivacyChange = (e) => {
    const { name, checked } = e.target;
    setPrivacySettings(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (user) {
        setLoading(true);
        try {
          const result = await getProfile();
          if (result.success) {
            setProfileData({
              firstName: result.data.firstName,
              lastName: result.data.lastName,
              email: result.data.email,
              phone: result.data.phone || '',
              photoUrl: result.data.profileImage || '',
              bio: result.data.bio || '',
              languages: ['English'] // Default language
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setMessage({
            type: 'error',
            text: 'Failed to load profile data. Please try again.'
          });
        } finally {
          setLoading(false);
        }
      }
    };

    fetchUserData();
  }, [user, getProfile]);

  // Handle photo upload
  const handlePhotoChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      setMessage({
        type: 'error',
        text: 'Please select an image file (JPG, PNG, etc.)'
      });
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setMessage({
        type: 'error',
        text: 'Image size should be less than 5MB'
      });
      return;
    }

    setUploadingImage(true);
    setMessage({ type: '', text: '' });

    try {
      const result = await uploadProfileImage(file);

      if (result.success) {
        setProfileData(prev => ({
          ...prev,
          photoUrl: result.data.profileImage
        }));

        setMessage({
          type: 'success',
          text: 'Profile image uploaded successfully!'
        });
      } else {
        setMessage({
          type: 'error',
          text: result.message || 'Failed to upload image. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setMessage({
        type: 'error',
        text: 'An error occurred. Please try again.'
      });
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle form submissions
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setMessage({ type: '', text: '' });

    try {
      // Prepare the data for the API
      const userData = {
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        phone: profileData.phone,
        bio: profileData.bio
      };

      const result = await updateProfile(userData);

      if (result.success) {
        setMessage({
          type: 'success',
          text: 'Profile updated successfully!'
        });

        // Scroll to top to show the message
        window.scrollTo(0, 0);
      } else {
        setMessage({
          type: 'error',
          text: result.message || 'Failed to update profile. Please try again.'
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({
        type: 'error',
        text: 'An error occurred. Please try again.'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleNotificationsSubmit = (e) => {
    e.preventDefault();
    // In a real app, send the data to an API
    alert('Notification settings updated successfully!');
  };

  const handleAccountSubmit = (e) => {
    e.preventDefault();
    // In a real app, send the data to an API
    alert('Account settings updated successfully!');
  };

  const handlePrivacySubmit = (e) => {
    e.preventDefault();
    // In a real app, send the data to an API
    alert('Privacy settings updated successfully!');
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();

    // Simple validation
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match!');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      alert('New password must be at least 8 characters long!');
      return;
    }

    // In a real app, send the data to an API
    alert('Password changed successfully!');

    // Clear the form
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout>
          <Head>
            <title>Account Settings | Istanbul Rooms</title>
            <meta name="description" content="Manage your account settings and preferences" />
          </Head>
          <div className="bg-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <Head>
          <title>Account Settings | Istanbul Rooms</title>
          <meta name="description" content="Manage your account settings and preferences" />
        </Head>

      <div className="bg-gray-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full md:w-1/4">
              <DashboardNav />

              {/* Account Verification */}
              <div className="mt-6 bg-white rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Account Status</h3>
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Email Verified</p>
                    <p className="text-xs text-gray-500">Your email has been verified</p>
                  </div>
                </div>

                <div className="mt-3 flex items-center">
                  <div className="h-8 w-8 bg-red-100 rounded-full flex items-center justify-center text-red-600 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Phone Not Verified</p>
                    <button className="text-xs text-blue-600 hover:text-blue-800">
                      Verify now
                    </button>
                  </div>
                </div>

                <div className="mt-3 flex items-center">
                  <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600 mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">ID Verification</p>
                    <button className="text-xs text-blue-600 hover:text-blue-800">
                      Complete verification
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="w-full md:w-3/4">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                {/* Tabs */}
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex">
                    <button
                      onClick={() => setActiveTab('profile')}
                      className={`py-4 px-6 text-sm font-medium ${
                        activeTab === 'profile'
                          ? 'border-b-2 border-blue-500 text-blue-600'
                          : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Profile
                    </button>
                    <button
                      onClick={() => setActiveTab('notifications')}
                      className={`py-4 px-6 text-sm font-medium ${
                        activeTab === 'notifications'
                          ? 'border-b-2 border-blue-500 text-blue-600'
                          : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Notifications
                    </button>
                    <button
                      onClick={() => setActiveTab('account')}
                      className={`py-4 px-6 text-sm font-medium ${
                        activeTab === 'account'
                          ? 'border-b-2 border-blue-500 text-blue-600'
                          : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Account
                    </button>
                    <button
                      onClick={() => setActiveTab('privacy')}
                      className={`py-4 px-6 text-sm font-medium ${
                        activeTab === 'privacy'
                          ? 'border-b-2 border-blue-500 text-blue-600'
                          : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Privacy
                    </button>
                    <button
                      onClick={() => setActiveTab('password')}
                      className={`py-4 px-6 text-sm font-medium ${
                        activeTab === 'password'
                          ? 'border-b-2 border-blue-500 text-blue-600'
                          : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      Password
                    </button>
                  </nav>
                </div>

                {/* Profile Tab */}
                {activeTab === 'profile' && (
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Profile Information</h2>
                    <p className="text-sm text-gray-600 mb-6">
                      Update your profile information and how you appear to others on the platform.
                    </p>

                    {/* Status Messages */}
                    {message.text && (
                      <div className={`mb-6 p-4 rounded-md ${
                        message.type === 'success'
                          ? 'bg-green-50 border-l-4 border-green-500 text-green-700'
                          : 'bg-red-50 border-l-4 border-red-500 text-red-700'
                      }`}>
                        {message.text}
                      </div>
                    )}

                    <form onSubmit={handleProfileSubmit}>
                      <div className="space-y-6">
                        {/* Profile Photo */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Profile Photo
                          </label>
                          <div className="flex items-center">
                            <div className="relative mr-4">
                              {profileData.photoUrl ? (
                                <div className="h-20 w-20 rounded-full overflow-hidden bg-gray-100">
                                  <img
                                    src={profileData.photoUrl}
                                    alt="Profile"
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              ) : (
                                <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
                                  <span className="text-gray-500 text-2xl">
                                    {profileData.firstName ? profileData.firstName.charAt(0) : 'U'}
                                  </span>
                                </div>
                              )}
                              {uploadingImage && (
                                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                                </div>
                              )}
                            </div>
                            <div>
                              <input
                                type="file"
                                ref={fileInputRef}
                                onChange={handlePhotoChange}
                                className="hidden"
                                accept="image/*"
                              />
                              <button
                                type="button"
                                onClick={triggerFileInput}
                                disabled={uploadingImage}
                                className={`px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 ${uploadingImage ? 'opacity-50 cursor-not-allowed' : ''} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                {uploadingImage ? 'Uploading...' : 'Upload Photo'}
                              </button>
                              <p className="mt-1 text-xs text-gray-500">
                                JPG, PNG or GIF. Max size 5MB.
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* First Name */}
                        <div>
                          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                            First Name
                          </label>
                          <input
                            type="text"
                            id="firstName"
                            name="firstName"
                            value={profileData.firstName}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>

                        {/* Last Name */}
                        <div>
                          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                            Last Name
                          </label>
                          <input
                            type="text"
                            id="lastName"
                            name="lastName"
                            value={profileData.lastName}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>

                        {/* Email */}
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                            Email Address
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={profileData.email}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            disabled
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            Email cannot be changed
                          </p>
                        </div>

                        {/* Phone */}
                        <div>
                          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                            Phone Number
                          </label>
                          <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={profileData.phone}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        {/* Bio */}
                        <div>
                          <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                            Bio
                          </label>
                          <textarea
                            id="bio"
                            name="bio"
                            rows={4}
                            value={profileData.bio}
                            onChange={handleProfileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Tell others about yourself..."
                          ></textarea>
                          <p className="mt-1 text-xs text-gray-500">
                            Brief description for your profile. This will help others get to know you.
                          </p>
                        </div>

                        {/* Languages */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Languages
                          </label>
                          <div className="flex flex-wrap gap-2">
                            {profileData.languages.map((language, index) => (
                              <div key={index} className="flex items-center bg-blue-100 text-blue-800 text-sm rounded-full px-3 py-1">
                                <span>{language}</span>
                                <button
                                  type="button"
                                  onClick={() => {
                                    setProfileData(prev => ({
                                      ...prev,
                                      languages: prev.languages.filter((_, i) => i !== index)
                                    }));
                                  }}
                                  className="ml-1 text-blue-500 hover:text-blue-700"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                            <button
                              type="button"
                              className="flex items-center text-sm text-gray-600 hover:text-gray-800 bg-gray-100 hover:bg-gray-200 rounded-full px-3 py-1"
                              onClick={() => {
                                const newLanguage = prompt('Add a language');
                                if (newLanguage && !profileData.languages.includes(newLanguage)) {
                                  setProfileData(prev => ({
                                    ...prev,
                                    languages: [...prev.languages, newLanguage]
                                  }));
                                }
                              }}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                              </svg>
                              Add Language
                            </button>
                          </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            disabled={saving}
                            className={`inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${saving ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                          >
                            {saving ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Saving...
                              </>
                            ) : 'Save Changes'}
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}

                {/* Notifications Tab */}
                {activeTab === 'notifications' && (
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Notification Settings</h2>
                    <p className="text-sm text-gray-600 mb-6">
                      Control how you receive notifications from Istanbul Rooms.
                    </p>

                    <form onSubmit={handleNotificationsSubmit}>
                      <div className="space-y-6">
                        <div className="border-b border-gray-200 pb-5">
                          <h3 className="text-md font-medium text-gray-900 mb-4">Email Notifications</h3>

                          <div className="space-y-4">
                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="emailNewMessages"
                                  name="emailNewMessages"
                                  type="checkbox"
                                  checked={notificationSettings.emailNewMessages}
                                  onChange={handleNotificationChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="emailNewMessages" className="font-medium text-gray-700">
                                  New Messages
                                </label>
                                <p className="text-gray-500">
                                  Receive email notifications when you get new messages
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="emailListingUpdates"
                                  name="emailListingUpdates"
                                  type="checkbox"
                                  checked={notificationSettings.emailListingUpdates}
                                  onChange={handleNotificationChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="emailListingUpdates" className="font-medium text-gray-700">
                                  Listing Updates
                                </label>
                                <p className="text-gray-500">
                                  Receive email notifications about updates to listings you're interested in
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="emailNewsletters"
                                  name="emailNewsletters"
                                  type="checkbox"
                                  checked={notificationSettings.emailNewsletters}
                                  onChange={handleNotificationChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="emailNewsletters" className="font-medium text-gray-700">
                                  Newsletters & Promotions
                                </label>
                                <p className="text-gray-500">
                                  Receive emails about Istanbul Rooms news, tips, and special offers
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="border-b border-gray-200 pb-5">
                          <h3 className="text-md font-medium text-gray-900 mb-4">SMS Notifications</h3>

                          <div className="space-y-4">
                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="smsNewMessages"
                                  name="smsNewMessages"
                                  type="checkbox"
                                  checked={notificationSettings.smsNewMessages}
                                  onChange={handleNotificationChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="smsNewMessages" className="font-medium text-gray-700">
                                  New Messages
                                </label>
                                <p className="text-gray-500">
                                  Receive SMS alerts when you get new messages
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="smsListingUpdates"
                                  name="smsListingUpdates"
                                  type="checkbox"
                                  checked={notificationSettings.smsListingUpdates}
                                  onChange={handleNotificationChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="smsListingUpdates" className="font-medium text-gray-700">
                                  Listing Updates
                                </label>
                                <p className="text-gray-500">
                                  Receive SMS alerts about important updates to your listings or saved listings
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Save Changes
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}

                {/* Account Tab */}
                {activeTab === 'account' && (
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Account Settings</h2>
                    <p className="text-sm text-gray-600 mb-6">
                      Manage your account settings and preferences.
                    </p>

                    <form onSubmit={handleAccountSubmit}>
                      <div className="space-y-6">
                        {/* Currency */}
                        <div>
                          <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                            Preferred Currency
                          </label>
                          <select
                            id="currency"
                            name="currency"
                            value={accountSettings.currency}
                            onChange={handleAccountChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="TRY">Turkish Lira (TRY)</option>
                            <option value="USD">US Dollar (USD)</option>
                            <option value="EUR">Euro (EUR)</option>
                            <option value="GBP">British Pound (GBP)</option>
                          </select>
                        </div>

                        {/* Time Zone */}
                        <div>
                          <label htmlFor="timeZone" className="block text-sm font-medium text-gray-700 mb-1">
                            Time Zone
                          </label>
                          <select
                            id="timeZone"
                            name="timeZone"
                            value={accountSettings.timeZone}
                            onChange={handleAccountChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="Europe/Istanbul">Istanbul (GMT+3)</option>
                            <option value="Europe/London">London (GMT+0/+1)</option>
                            <option value="America/New_York">New York (GMT-5/-4)</option>
                            <option value="Asia/Dubai">Dubai (GMT+4)</option>
                            <option value="Asia/Tokyo">Tokyo (GMT+9)</option>
                          </select>
                        </div>

                        {/* Language */}
                        <div>
                          <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-1">
                            Language
                          </label>
                          <select
                            id="language"
                            name="language"
                            value={accountSettings.language}
                            onChange={handleAccountChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="English">English</option>
                            <option value="Turkish">Turkish</option>
                            <option value="Arabic">Arabic</option>
                            <option value="French">French</option>
                            <option value="German">German</option>
                            <option value="Russian">Russian</option>
                          </select>
                        </div>

                        {/* Danger Zone */}
                        <div className="pt-6 border-t border-gray-200">
                          <h3 className="text-md font-medium text-red-600 mb-4">Danger Zone</h3>

                          <div className="bg-red-50 rounded-md p-4">
                            <div className="flex">
                              <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">Delete Account</h3>
                                <div className="mt-2 text-sm text-red-700">
                                  <p>
                                    Once you delete your account, there is no going back. Please be certain.
                                  </p>
                                </div>
                                <div className="mt-4">
                                  <button
                                    type="button"
                                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                  >
                                    Delete Account
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Save Changes
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}

                {/* Privacy Tab */}
                {activeTab === 'privacy' && (
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Privacy Settings</h2>
                    <p className="text-sm text-gray-600 mb-6">
                      Control who can see your information and how your data is used.
                    </p>

                    <form onSubmit={handlePrivacySubmit}>
                      <div className="space-y-6">
                        <div className="border-b border-gray-200 pb-5">
                          <h3 className="text-md font-medium text-gray-900 mb-4">Profile Visibility</h3>

                          <div className="space-y-4">
                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="showContactInfo"
                                  name="showContactInfo"
                                  type="checkbox"
                                  checked={privacySettings.showContactInfo}
                                  onChange={handlePrivacyChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="showContactInfo" className="font-medium text-gray-700">
                                  Show Contact Information
                                </label>
                                <p className="text-gray-500">
                                  Allow others to see your email and phone number
                                </p>
                              </div>
                            </div>

                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="showSocialMedia"
                                  name="showSocialMedia"
                                  type="checkbox"
                                  checked={privacySettings.showSocialMedia}
                                  onChange={handlePrivacyChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="showSocialMedia" className="font-medium text-gray-700">
                                  Show Social Media Links
                                </label>
                                <p className="text-gray-500">
                                  Display your connected social media profiles
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="border-b border-gray-200 pb-5">
                          <h3 className="text-md font-medium text-gray-900 mb-4">Location Settings</h3>

                          <div className="space-y-4">
                            <div className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id="allowLocationAccess"
                                  name="allowLocationAccess"
                                  type="checkbox"
                                  checked={privacySettings.allowLocationAccess}
                                  onChange={handlePrivacyChange}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor="allowLocationAccess" className="font-medium text-gray-700">
                                  Allow Location Access
                                </label>
                                <p className="text-gray-500">
                                  Enable location-based features and nearby listings
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Privacy Links */}
                        <div className="text-sm">
                          <p className="text-gray-600">
                            For more information on how we handle your data, please read our
                            {' '}
                            <a href="/privacy" className="text-blue-600 hover:text-blue-800">
                              Privacy Policy
                            </a>
                            {' '}and{' '}
                            <a href="/terms" className="text-blue-600 hover:text-blue-800">
                              Terms of Service
                            </a>.
                          </p>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Save Changes
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}

                {/* Password Tab */}
                {activeTab === 'password' && (
                  <div className="p-6">
                    <h2 className="text-lg font-bold text-gray-900 mb-4">Change Password</h2>
                    <p className="text-sm text-gray-600 mb-6">
                      Update your password to keep your account secure.
                    </p>

                    <form onSubmit={handlePasswordSubmit}>
                      <div className="space-y-6">
                        {/* Current Password */}
                        <div>
                          <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                            Current Password
                          </label>
                          <input
                            type="password"
                            id="currentPassword"
                            name="currentPassword"
                            value={passwordData.currentPassword}
                            onChange={handlePasswordChange}
                            required
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        {/* New Password */}
                        <div>
                          <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                            New Password
                          </label>
                          <input
                            type="password"
                            id="newPassword"
                            name="newPassword"
                            value={passwordData.newPassword}
                            onChange={handlePasswordChange}
                            required
                            minLength={8}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            Password must be at least 8 characters long
                          </p>
                        </div>

                        {/* Confirm Password */}
                        <div>
                          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                            Confirm New Password
                          </label>
                          <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            value={passwordData.confirmPassword}
                            onChange={handlePasswordChange}
                            required
                            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        {/* Forgot Password Link */}
                        <div className="text-sm">
                          <a href="/auth/forgot-password" className="text-blue-600 hover:text-blue-800">
                            Forgot your password?
                          </a>
                        </div>

                        {/* Submit Button */}
                        <div className="flex justify-end">
                          <button
                            type="submit"
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Update Password
                          </button>
                        </div>
                      </div>
                    </form>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
    </ProtectedRoute>
  );
}