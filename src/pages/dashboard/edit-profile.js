// pages/dashboard/edit-profile.js
import { useState, useEffect, useRef } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import ProtectedRoute from '../../components/auth/ProtectedRoute';
import { useAuth } from '../../utils/authContext';

export default function EditProfile() {
  const router = useRouter();
  const { user, getProfile, updateProfile, uploadProfileImage } = useAuth();
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const fileInputRef = useRef(null);

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (user) {
        setLoading(true);
        try {
          const result = await getProfile();
          if (result.success) {
            setUserData(result.data);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    
    fetchUserData();
  }, [user, getProfile]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      // Handle nested fields (e.g., preferences.smoking)
      const [parent, child] = name.split('.');
      setUserData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      // Handle regular fields
      setUserData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setMessage({ type: '', text: '' });
    
    try {
      const result = await updateProfile(userData);
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: 'Profile updated successfully!' 
        });
        
        // Scroll to top to show the message
        window.scrollTo(0, 0);
      } else {
        setMessage({ 
          type: 'error', 
          text: result.message || 'Failed to update profile. Please try again.' 
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({ 
        type: 'error', 
        text: 'An error occurred. Please try again.' 
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle profile image upload
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      setMessage({ 
        type: 'error', 
        text: 'Please select an image file (JPG, PNG, etc.)' 
      });
      return;
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setMessage({ 
        type: 'error', 
        text: 'Image size should be less than 5MB' 
      });
      return;
    }
    
    setUploadingImage(true);
    setMessage({ type: '', text: '' });
    
    try {
      const result = await uploadProfileImage(file);
      
      if (result.success) {
        setUserData(prev => ({
          ...prev,
          profileImage: result.data.profileImage
        }));
        
        setMessage({ 
          type: 'success', 
          text: 'Profile image uploaded successfully!' 
        });
      } else {
        setMessage({ 
          type: 'error', 
          text: result.message || 'Failed to upload image. Please try again.' 
        });
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      setMessage({ 
        type: 'error', 
        text: 'An error occurred. Please try again.' 
      });
    } finally {
      setUploadingImage(false);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Layout>
          <Head>
            <title>Edit Profile | Istanbul Rooms</title>
            <meta name="description" content="Edit your profile information" />
          </Head>
          <div className="bg-gray-100 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            </div>
          </div>
        </Layout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Layout>
        <Head>
          <title>Edit Profile | Istanbul Rooms</title>
          <meta name="description" content="Edit your profile information" />
        </Head>
        
        <div className="bg-gray-100 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row gap-8">
              {/* Sidebar */}
              <div className="w-full md:w-1/4">
                <DashboardNav />
              </div>
              
              {/* Main Content */}
              <div className="w-full md:w-3/4">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h1 className="text-2xl font-bold text-gray-900 mb-6">Edit Profile</h1>
                  
                  {/* Status Messages */}
                  {message.text && (
                    <div className={`mb-6 p-4 rounded-md ${
                      message.type === 'success' 
                        ? 'bg-green-50 border-l-4 border-green-500 text-green-700' 
                        : 'bg-red-50 border-l-4 border-red-500 text-red-700'
                    }`}>
                      {message.text}
                    </div>
                  )}
                  
                  {userData && (
                    <form onSubmit={handleSubmit}>
                      {/* Profile Image Section */}
                      <div className="mb-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Profile Image</h2>
                        <div className="flex items-center">
                          <div className="relative">
                            <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-200">
                              {userData.profileImage ? (
                                <img 
                                  src={userData.profileImage} 
                                  alt="Profile" 
                                  className="h-full w-full object-cover"
                                />
                              ) : (
                                <div className="h-full w-full flex items-center justify-center bg-gray-200 text-gray-500">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                  </svg>
                                </div>
                              )}
                            </div>
                            {uploadingImage && (
                              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                              </div>
                            )}
                          </div>
                          <div className="ml-5">
                            <input
                              type="file"
                              ref={fileInputRef}
                              onChange={handleImageUpload}
                              className="hidden"
                              accept="image/*"
                            />
                            <button
                              type="button"
                              onClick={triggerFileInput}
                              disabled={uploadingImage}
                              className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                                uploadingImage ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
                              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                            >
                              {uploadingImage ? 'Uploading...' : 'Change Image'}
                            </button>
                            <p className="mt-1 text-xs text-gray-500">
                              JPG, PNG or GIF. Max size 5MB.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Basic Information */}
                      <div className="mb-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                              First Name
                            </label>
                            <input
                              type="text"
                              id="firstName"
                              name="firstName"
                              value={userData.firstName || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              required
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                              Last Name
                            </label>
                            <input
                              type="text"
                              id="lastName"
                              name="lastName"
                              value={userData.lastName || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              required
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                              Email
                            </label>
                            <input
                              type="email"
                              id="email"
                              name="email"
                              value={userData.email || ''}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 bg-gray-100 sm:text-sm"
                              disabled
                            />
                            <p className="mt-1 text-xs text-gray-500">
                              Email cannot be changed
                            </p>
                          </div>
                          
                          <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                              Phone Number
                            </label>
                            <input
                              type="tel"
                              id="phone"
                              name="phone"
                              value={userData.phone || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div className="md:col-span-2">
                            <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                              Bio
                            </label>
                            <textarea
                              id="bio"
                              name="bio"
                              rows={4}
                              value={userData.bio || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              placeholder="Tell us about yourself..."
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="occupation" className="block text-sm font-medium text-gray-700">
                              Occupation
                            </label>
                            <input
                              type="text"
                              id="occupation"
                              name="occupation"
                              value={userData.occupation || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                        </div>
                      </div>
                      
                      {/* Location Information */}
                      <div className="mb-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Location</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="md:col-span-2">
                            <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                              Address
                            </label>
                            <input
                              type="text"
                              id="address"
                              name="address"
                              value={userData.address || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="city" className="block text-sm font-medium text-gray-700">
                              City
                            </label>
                            <input
                              type="text"
                              id="city"
                              name="city"
                              value={userData.city || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="country" className="block text-sm font-medium text-gray-700">
                              Country
                            </label>
                            <input
                              type="text"
                              id="country"
                              name="country"
                              value={userData.country || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                        </div>
                      </div>
                      
                      {/* Preferences */}
                      <div className="mb-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Preferences</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label htmlFor="preferences.gender" className="block text-sm font-medium text-gray-700">
                              Gender
                            </label>
                            <select
                              id="preferences.gender"
                              name="preferences.gender"
                              value={userData.preferences?.gender || ''}
                              onChange={handleChange}
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            >
                              <option value="">Prefer not to say</option>
                              <option value="male">Male</option>
                              <option value="female">Female</option>
                              <option value="other">Other</option>
                            </select>
                          </div>
                          
                          <div>
                            <label htmlFor="preferences.ageRange" className="block text-sm font-medium text-gray-700">
                              Age Range
                            </label>
                            <input
                              type="text"
                              id="preferences.ageRange"
                              name="preferences.ageRange"
                              value={userData.preferences?.ageRange || ''}
                              onChange={handleChange}
                              placeholder="e.g., 25-30"
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div className="flex items-center">
                            <input
                              id="preferences.smoking"
                              name="preferences.smoking"
                              type="checkbox"
                              checked={userData.preferences?.smoking || false}
                              onChange={handleChange}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="preferences.smoking" className="ml-2 block text-sm text-gray-700">
                              Smoking
                            </label>
                          </div>
                          
                          <div className="flex items-center">
                            <input
                              id="preferences.pets"
                              name="preferences.pets"
                              type="checkbox"
                              checked={userData.preferences?.pets || false}
                              onChange={handleChange}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="preferences.pets" className="ml-2 block text-sm text-gray-700">
                              Pets
                            </label>
                          </div>
                        </div>
                      </div>
                      
                      {/* Social Links */}
                      <div className="mb-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Social Links</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label htmlFor="socialLinks.facebook" className="block text-sm font-medium text-gray-700">
                              Facebook
                            </label>
                            <input
                              type="url"
                              id="socialLinks.facebook"
                              name="socialLinks.facebook"
                              value={userData.socialLinks?.facebook || ''}
                              onChange={handleChange}
                              placeholder="https://facebook.com/username"
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="socialLinks.twitter" className="block text-sm font-medium text-gray-700">
                              Twitter
                            </label>
                            <input
                              type="url"
                              id="socialLinks.twitter"
                              name="socialLinks.twitter"
                              value={userData.socialLinks?.twitter || ''}
                              onChange={handleChange}
                              placeholder="https://twitter.com/username"
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="socialLinks.instagram" className="block text-sm font-medium text-gray-700">
                              Instagram
                            </label>
                            <input
                              type="url"
                              id="socialLinks.instagram"
                              name="socialLinks.instagram"
                              value={userData.socialLinks?.instagram || ''}
                              onChange={handleChange}
                              placeholder="https://instagram.com/username"
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                          
                          <div>
                            <label htmlFor="socialLinks.linkedin" className="block text-sm font-medium text-gray-700">
                              LinkedIn
                            </label>
                            <input
                              type="url"
                              id="socialLinks.linkedin"
                              name="socialLinks.linkedin"
                              value={userData.socialLinks?.linkedin || ''}
                              onChange={handleChange}
                              placeholder="https://linkedin.com/in/username"
                              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            />
                          </div>
                        </div>
                      </div>
                      
                      {/* Submit Button */}
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={saving}
                          className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
                            saving ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
                          } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                        >
                          {saving ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Saving...
                            </>
                          ) : 'Save Changes'}
                        </button>
                      </div>
                    </form>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </ProtectedRoute>
  );
}
