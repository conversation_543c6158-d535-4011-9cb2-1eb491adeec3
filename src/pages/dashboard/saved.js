// pages/dashboard/saved.js
import { useState, useEffect } from 'react';
import Head from 'next/head';
import Layout from '../../components/layout/Layout';
import DashboardNav from '../../components/dashboard/DashboardNav';
import SavedListings from '../../components/dashboard/SavedListings';
import { dummyListings } from '../../utils/dummyData';

export default function SavedListingsPage() {
  const [savedListings, setSavedListings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // In a real app, you would fetch user's saved listings from an API
  useEffect(() => {
    // Simulate API call to get saved listings
    setTimeout(() => {
      // Create dummy saved listings with additional properties
      const listings = dummyListings.slice(0, 8).map((listing, index) => ({
        ...listing,
        savedDate: index === 0 ? 'today' : index < 3 ? 'yesterday' : '2 weeks ago',
        collection: index < 2 ? 'Favorites' : index < 4 ? 'To Visit' : index < 6 ? 'Student Housing' : null
      }));
      
      setSavedListings(listings);
      setIsLoading(false);
    }, 800);
  }, []);

  return (
    <Layout>
      <Head>
        <title>Saved Listings | Istanbul Rooms</title>
        <meta name="description" content="View and organize your saved listings" />
      </Head>
      
      <div className="bg-gray-100 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-8">
            {/* Sidebar */}
            <div className="w-full md:w-1/4">
              <DashboardNav />
              
              {/* Collections Info */}
              <div className="mt-6 bg-white rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-3">My Collections</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center py-2">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-md bg-red-100 flex items-center justify-center text-red-600 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-800">Favorites</span>
                    </div>
                    <span className="text-gray-500 text-sm">2 listings</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-md bg-green-100 flex items-center justify-center text-green-600 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-800">To Visit</span>
                    </div>
                    <span className="text-gray-500 text-sm">2 listings</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-md bg-blue-100 flex items-center justify-center text-blue-600 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                        </svg>
                      </div>
                      <span className="text-gray-800">Student Housing</span>
                    </div>
                    <span className="text-gray-500 text-sm">2 listings</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-md bg-gray-100 flex items-center justify-center text-gray-600 mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span className="text-gray-800">Uncategorized</span>
                    </div>
                    <span className="text-gray-500 text-sm">2 listings</span>
                  </div>
                </div>
                
                <button className="mt-4 text-blue-600 text-sm font-medium inline-flex items-center w-full justify-center py-2 px-3 border border-blue-300 rounded-md hover:bg-blue-50">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Create New Collection
                </button>
              </div>
              
              {/* Saving Tips */}
              <div className="mt-6 bg-blue-50 rounded-lg shadow-md p-4">
                <h3 className="font-medium text-gray-900 mb-2">Pro Tip</h3>
                <p className="text-sm text-gray-600 mb-2">
                  Use collections to organize your saved listings based on your preferences or housing goals.
                </p>
                <p className="text-sm text-gray-600">
                  You can also share collections with roommates or friends to help make decisions together.
                </p>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="w-full md:w-3/4">
              {isLoading ? (
                <div className="bg-white rounded-lg shadow-md p-6 flex justify-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <SavedListings listings={savedListings} />
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}