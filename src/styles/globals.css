@tailwind base;
@tailwind components;
@tailwind utilities;

/* Additional global styles can be added here */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Custom utility classes */
@layer components {
  .btn-primary {
    @apply px-8 py-4 text-lg font-medium rounded-lg shadow-lg text-white bg-blue-800 hover:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
  }

  .btn-secondary {
    @apply px-8 py-4 text-lg font-medium rounded-lg shadow-lg text-blue-800 bg-white hover:bg-blue-50 border border-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200;
  }

  .search-input {
    @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-600 transition-all duration-200;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200;
  }

  .text-primary {
    @apply text-blue-800;
  }

  .text-secondary {
    @apply text-blue-600;
  }

  .bg-primary {
    @apply bg-blue-800;
  }

  .bg-secondary {
    @apply bg-blue-600;
  }
}
