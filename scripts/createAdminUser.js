/**
 * <PERSON><PERSON><PERSON> to create the default admin user
 * Run with: node scripts/createAdminUser.js
 */

const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');

// Admin user credentials
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'admin@12345';

// Create admin user if it doesn't exist
async function createAdminUser() {
  let client;
  
  try {
    // Connect to MongoDB
    const uri = 'mongodb+srv://imrangulhassan:<EMAIL>/?retryWrites=true&w=majority&appName=RoomShare';
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB Atlas');
    
    const db = client.db();
    const usersCollection = db.collection('users');
    
    // Check if admin user already exists
    const existingAdmin = await usersCollection.findOne({ email: ADMIN_EMAIL });
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      console.log(`Email: ${ADMIN_EMAIL}`);
      console.log(`Role: ${existingAdmin.role}`);
      console.log(`Account Type: ${existingAdmin.accountType}`);
      return;
    }
    
    // Hash the password using bcryptjs (same as the User model)
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(ADMIN_PASSWORD, salt);
    
    // Create admin user
    const adminUser = {
      firstName: 'Admin',
      lastName: 'User',
      email: ADMIN_EMAIL,
      password: hashedPassword,
      accountType: 'admin',
      role: 'admin',
      authProviders: ['credentials'],
      isVerified: true, // Admin is pre-verified
      verificationToken: null,
      verificationTokenExpires: null,
      resetPasswordToken: null,
      resetPasswordExpires: null,
      profileImage: '',
      bio: 'System Administrator with full platform access',
      phone: '',
      address: '',
      city: '',
      country: '',
      occupation: 'Administrator',
      preferences: {
        smoking: false,
        pets: false,
        gender: '',
        ageRange: ''
      },
      socialLinks: {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: ''
      },
      emailNotifications: {
        messages: true,
        listings: true,
        marketing: false
      },
      pushNotifications: {
        messages: true,
        listings: true,
        system: true
      },
      privacySettings: {
        showProfile: false,
        showContact: false,
        showSocialLinks: false
      },
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert admin user
    const result = await usersCollection.insertOne(adminUser);
    
    console.log('🎉 Successfully created admin user!');
    console.log(`User ID: ${result.insertedId}`);
    console.log('');
    console.log('=== ADMIN LOGIN CREDENTIALS ===');
    console.log(`Email: ${ADMIN_EMAIL}`);
    console.log(`Password: ${ADMIN_PASSWORD}`);
    console.log(`Role: admin`);
    console.log(`Account Type: admin`);
    console.log('================================');
    console.log('');
    console.log('The admin user can now:');
    console.log('- Access the admin dashboard at /admin');
    console.log('- Manage all users and listings');
    console.log('- View platform analytics');
    console.log('- Moderate content');
    console.log('- Access all admin features');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the script
if (require.main === module) {
  console.log('🚀 Creating admin user...');
  createAdminUser().catch(console.error);
}

module.exports = { createAdminUser };
