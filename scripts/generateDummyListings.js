/**
 * <PERSON><PERSON><PERSON> to generate dummy listings around a specific location
 * Run with: node scripts/generateDummyListings.js
 */

const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

// User's exact coordinates for Hyderabad, Pakistan
const CENTER_LAT = 27.5382272;
const CENTER_LNG = 68.2033152;
const NUM_LISTINGS = 20;
const MAX_DISTANCE_KM = 10; // Maximum distance in kilometers

// Random data for listings
const propertyTypes = ['Apartment', 'House', 'Studio', 'Room', 'Villa', 'Duplex', 'Penthouse'];
const amenities = ['WiFi', 'AC', 'Heating', 'Kitchen', 'Washer', 'Dryer', 'TV', 'Parking', 'Elevator', 'Pool', 'Gym'];
const neighborhoods = ['Downtown', 'Westside', 'Northend', 'Eastview', 'Southpark', 'Central', 'University', 'Industrial', 'Old Town', 'Riverside'];
const streetNames = ['Main', 'Oak', 'Maple', 'Park', 'Pine', 'Cedar', 'Elm', 'Washington', 'Lake', 'Hill'];

// Generate a random coordinate within a certain distance of the center point
function generateRandomCoordinate(centerLat, centerLng, maxDistanceKm) {
  // Earth's radius in kilometers
  const earthRadius = 6371;
  
  // Convert max distance from kilometers to radians
  const maxDistanceRadians = maxDistanceKm / earthRadius;
  
  // Generate a random distance within the maximum distance
  const randomDistance = Math.random() * maxDistanceRadians;
  
  // Generate a random angle in radians
  const randomAngle = Math.random() * 2 * Math.PI;
  
  // Calculate the new latitude
  const newLat = Math.asin(
    Math.sin(centerLat * (Math.PI / 180)) * Math.cos(randomDistance) +
    Math.cos(centerLat * (Math.PI / 180)) * Math.sin(randomDistance) * Math.cos(randomAngle)
  ) * (180 / Math.PI);
  
  // Calculate the new longitude
  const newLng = centerLng * (Math.PI / 180) + Math.atan2(
    Math.sin(randomAngle) * Math.sin(randomDistance) * Math.cos(centerLat * (Math.PI / 180)),
    Math.cos(randomDistance) - Math.sin(centerLat * (Math.PI / 180)) * Math.sin(newLat * (Math.PI / 180))
  ) * (180 / Math.PI);
  
  return { lat: newLat, lng: newLng };
}

// Generate a random price between min and max
function generateRandomPrice(min, max) {
  return Math.floor(Math.random() * (max - min + 1) + min);
}

// Generate random amenities
function generateRandomAmenities() {
  const numAmenities = Math.floor(Math.random() * 6) + 3; // 3-8 amenities
  const selectedAmenities = [];
  
  for (let i = 0; i < numAmenities; i++) {
    const amenity = amenities[Math.floor(Math.random() * amenities.length)];
    if (!selectedAmenities.includes(amenity)) {
      selectedAmenities.push(amenity);
    }
  }
  
  return {
    list: selectedAmenities,
    furnished: Math.random() > 0.3, // 70% chance of being furnished
    petsAllowed: Math.random() > 0.5, // 50% chance of allowing pets
    smokingAllowed: Math.random() > 0.7, // 30% chance of allowing smoking
  };
}

// Generate a random date in the future (0-60 days)
function generateFutureDate() {
  const today = new Date();
  const futureDate = new Date(today);
  futureDate.setDate(today.getDate() + Math.floor(Math.random() * 60));
  return futureDate;
}

// Generate a random listing
function generateListing(index) {
  // Generate coordinates within maxDistanceKm of the center
  const coordinates = generateRandomCoordinate(CENTER_LAT, CENTER_LNG, MAX_DISTANCE_KM);
  
  // Calculate actual distance from center in kilometers
  const lat1 = CENTER_LAT * (Math.PI / 180);
  const lon1 = CENTER_LNG * (Math.PI / 180);
  const lat2 = coordinates.lat * (Math.PI / 180);
  const lon2 = coordinates.lng * (Math.PI / 180);
  
  const dlon = lon2 - lon1;
  const dlat = lat2 - lat1;
  const a = Math.sin(dlat / 2) ** 2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dlon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distanceKm = 6371 * c; // Earth's radius is 6371 km
  
  // Generate a street number and name
  const streetNumber = Math.floor(Math.random() * 200) + 1;
  const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
  
  // Generate a neighborhood
  const neighborhood = neighborhoods[Math.floor(Math.random() * neighborhoods.length)];
  
  // Generate a property type
  const propertyType = propertyTypes[Math.floor(Math.random() * propertyTypes.length)];
  
  // Generate a price based on property type and distance
  let basePrice;
  switch (propertyType) {
    case 'Room':
      basePrice = 2000;
      break;
    case 'Studio':
      basePrice = 3500;
      break;
    case 'Apartment':
      basePrice = 5000;
      break;
    case 'House':
      basePrice = 8000;
      break;
    case 'Duplex':
      basePrice = 10000;
      break;
    case 'Villa':
      basePrice = 15000;
      break;
    case 'Penthouse':
      basePrice = 20000;
      break;
    default:
      basePrice = 5000;
  }
  
  // Adjust price based on distance from center (closer = more expensive)
  const distanceFactor = 1 - (distanceKm / (MAX_DISTANCE_KM * 1.5));
  const adjustedPrice = Math.round(basePrice * (0.8 + distanceFactor * 0.4));
  
  // Generate random rooms
  const bedrooms = Math.floor(Math.random() * 4) + 1; // 1-4 bedrooms
  const bathrooms = Math.floor(Math.random() * 3) + 1; // 1-3 bathrooms
  
  // Generate random size (square meters)
  const size = (propertyType === 'Room' || propertyType === 'Studio') 
    ? Math.floor(Math.random() * 30) + 20 // 20-50 m² for rooms/studios
    : Math.floor(Math.random() * 100) + 50; // 50-150 m² for other properties
  
  return {
    _id: uuidv4(),
    title: `${propertyType} in ${neighborhood}`,
    description: `Beautiful ${bedrooms} bedroom ${propertyType.toLowerCase()} located in the ${neighborhood} area. ${
      Math.random() > 0.5 ? 'Recently renovated with modern amenities.' : 'Featuring a spacious layout and great natural light.'
    } ${Math.random() > 0.7 ? 'Close to public transportation and shopping centers.' : 'Within walking distance to parks and restaurants.'}`,
    propertyType,
    price: {
      amount: adjustedPrice,
      currency: 'PKR',
      paymentPeriod: 'monthly'
    },
    rooms: {
      bedrooms,
      bathrooms,
      totalRooms: bedrooms + Math.floor(Math.random() * 3) + 1 // Add 1-3 additional rooms
    },
    size: {
      value: size,
      unit: 'sqm'
    },
    address: {
      street: `${streetNumber} ${streetName} St`,
      neighborhood,
      city: 'Hyderabad',
      country: 'Pakistan',
      postalCode: `${Math.floor(Math.random() * 90000) + 10000}`
    },
    location: {
      type: 'Point',
      coordinates: [coordinates.lng, coordinates.lat] // MongoDB uses [longitude, latitude] format
    },
    images: [
      {
        url: `https://source.unsplash.com/random/800x600?apartment,${index}`,
        caption: `${propertyType} exterior`
      },
      {
        url: `https://source.unsplash.com/random/800x600?interior,${index}`,
        caption: 'Living area'
      },
      {
        url: `https://source.unsplash.com/random/800x600?kitchen,${index}`,
        caption: 'Kitchen'
      }
    ],
    amenities: generateRandomAmenities(),
    availability: {
      availableFrom: generateFutureDate(),
      minimumStay: Math.floor(Math.random() * 11) + 1, // 1-12 months
      maximumStay: Math.floor(Math.random() * 24) + 12 // 12-36 months
    },
    verified: Math.random() > 0.3, // 70% chance of being verified
    featured: Math.random() > 0.8, // 20% chance of being featured
    createdAt: new Date(),
    updatedAt: new Date(),
    distance: distanceKm.toFixed(2) // Store the actual distance from the center point
  };
}

// Generate dummy listings and save to MongoDB
async function generateDummyListings() {
  let client;
  
  try {
    // Generate listings
    const listings = [];
    for (let i = 0; i < NUM_LISTINGS; i++) {
      listings.push(generateListing(i));
    }
    
    // Save to a JSON file as backup
    const outputPath = path.join(__dirname, '../data/dummyListings.json');
    
    // Create the directory if it doesn't exist
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Write the listings to the file
    fs.writeFileSync(outputPath, JSON.stringify(listings, null, 2));
    console.log(`Successfully generated ${listings.length} listings and saved to ${outputPath}`);
    
    // Connect to MongoDB and insert listings
    const uri = 'mongodb+srv://imrangulhassan:<EMAIL>/?retryWrites=true&w=majority&appName=RoomShare';
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB Atlas');
    
    const db = client.db();
    const listingsCollection = db.collection('listings');
    
    // Insert all listings
    const result = await listingsCollection.insertMany(listings);
    console.log(`Successfully inserted ${result.insertedCount} listings into MongoDB`);
    
    // Create a 2dsphere index for geospatial queries if it doesn't exist
    const indexes = await listingsCollection.indexes();
    const hasGeoIndex = indexes.some(index => index.name === 'location_2dsphere');
    
    if (!hasGeoIndex) {
      await listingsCollection.createIndex({ 'location': '2dsphere' });
      console.log('Created 2dsphere index on location field');
    }
    
  } catch (error) {
    console.error('Error generating and saving dummy listings:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Create admin user if it doesn't exist
async function createAdminUser() {
  let client;

  try {
    // Connect to MongoDB
    const uri = 'mongodb+srv://imrangulhassan:<EMAIL>/?retryWrites=true&w=majority&appName=RoomShare';
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB Atlas for admin user creation');

    const db = client.db();
    const usersCollection = db.collection('users');

    // Check if admin user already exists
    const existingAdmin = await usersCollection.findOne({ email: '<EMAIL>' });

    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Hash the password using bcryptjs (same as the User model)
    const bcrypt = require('bcryptjs');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin@12345', salt);

    // Create admin user
    const adminUser = {
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      accountType: 'admin',
      role: 'admin',
      authProviders: ['credentials'],
      isVerified: true, // Admin is pre-verified
      verificationToken: null,
      verificationTokenExpires: null,
      resetPasswordToken: null,
      resetPasswordExpires: null,
      profileImage: '',
      bio: 'System Administrator',
      phone: '',
      address: '',
      city: '',
      country: '',
      occupation: 'Administrator',
      preferences: {
        smoking: false,
        pets: false,
        gender: '',
        ageRange: ''
      },
      socialLinks: {
        facebook: '',
        twitter: '',
        instagram: '',
        linkedin: ''
      },
      emailNotifications: {
        messages: true,
        listings: true,
        marketing: false
      },
      pushNotifications: {
        messages: true,
        listings: true,
        system: true
      },
      privacySettings: {
        showProfile: false,
        showContact: false,
        showSocialLinks: false
      },
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert admin user
    const result = await usersCollection.insertOne(adminUser);
    console.log(`Successfully created admin user with ID: ${result.insertedId}`);
    console.log('Admin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin@12345');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run both scripts
async function runScripts() {
  console.log('=== Creating Admin User ===');
  await createAdminUser();

  console.log('\n=== Generating Dummy Listings ===');
  await generateDummyListings();
}

// Run the scripts
runScripts().catch(console.error);
