/**
 * Comprehensive Search Functionality Test Suite
 * Tests all search features with the populated database
 * Run with: node scripts/testSearchFunctionality.js
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// Test scenarios
const testScenarios = [
  {
    name: 'Basic Search - All Listings',
    url: '/api/listings/search?limit=5',
    expectedFields: ['data', 'totalCount', 'success'],
    validate: (response) => response.data.length <= 5 && response.totalCount > 0
  },
  {
    name: 'Property Type Filter - Apartments',
    url: '/api/listings/search?propertyType=apartment&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => item.propertyType === 'apartment')
  },
  {
    name: 'Price Range Filter',
    url: '/api/listings/search?priceMin=15000&priceMax=30000&limit=5',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => 
      item.price.amount >= 15000 && item.price.amount <= 30000
    )
  },
  {
    name: 'Text Search',
    url: '/api/listings/search?q=apartment&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.length > 0
  },
  {
    name: 'Bedroom Filter',
    url: '/api/listings/search?bedrooms=2&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => item.rooms.bedrooms === 2)
  },
  {
    name: 'Amenity Filter - WiFi',
    url: '/api/listings/search?amenities=wifi&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => item.amenities.wifi === true)
  },
  {
    name: 'Multiple Amenities Filter',
    url: '/api/listings/search?amenities=wifi,parking&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => 
      item.amenities.wifi === true && item.amenities.parking === true
    )
  },
  {
    name: 'City Filter',
    url: '/api/listings/search?city=Hyderabad&limit=5',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => 
      item.address.city.toLowerCase().includes('hyderabad')
    )
  },
  {
    name: 'Furnished Filter',
    url: '/api/listings/search?furnished=true&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => item.amenities.furnished === true)
  },
  {
    name: 'Featured Listings Sort',
    url: '/api/listings/search?sortBy=featured&limit=5',
    expectedFields: ['data'],
    validate: (response) => {
      const featured = response.data.filter(item => item.isFeatured);
      const nonFeatured = response.data.filter(item => !item.isFeatured);
      return featured.length === 0 || nonFeatured.length === 0 || 
             response.data.indexOf(featured[0]) < response.data.indexOf(nonFeatured[0]);
    }
  },
  {
    name: 'Price Sort - Low to High',
    url: '/api/listings/search?sortBy=price&sortOrder=asc&limit=5',
    expectedFields: ['data'],
    validate: (response) => {
      for (let i = 1; i < response.data.length; i++) {
        if (response.data[i].price.amount < response.data[i-1].price.amount) {
          return false;
        }
      }
      return true;
    }
  },
  {
    name: 'Combined Filters',
    url: '/api/listings/search?propertyType=apartment&priceMax=25000&amenities=wifi&limit=3',
    expectedFields: ['data'],
    validate: (response) => response.data.every(item => 
      item.propertyType === 'apartment' && 
      item.price.amount <= 25000 && 
      item.amenities.wifi === true
    )
  }
];

// Test runner
async function runTests() {
  console.log('🧪 Starting Search Functionality Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const test of testScenarios) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const response = await fetch(`${BASE_URL}${test.url}`);
      const data = await response.json();
      
      // Check if response is successful
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
      }
      
      // Check expected fields
      for (const field of test.expectedFields) {
        if (!(field in data)) {
          throw new Error(`Missing expected field: ${field}`);
        }
      }
      
      // Run custom validation
      if (test.validate && !test.validate(data)) {
        throw new Error('Custom validation failed');
      }
      
      console.log(`✅ PASSED - Found ${data.data?.length || 0} results\n`);
      passed++;
      
    } catch (error) {
      console.log(`❌ FAILED - ${error.message}\n`);
      failed++;
    }
  }
  
  // Summary
  console.log('📊 TEST SUMMARY');
  console.log('================');
  console.log(`Total Tests: ${testScenarios.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${Math.round((passed / testScenarios.length) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Search functionality is working correctly.');
  } else {
    console.log(`\n⚠️  ${failed} test(s) failed. Please check the implementation.`);
  }
}

// Additional database statistics test
async function testDatabaseStats() {
  console.log('\n📈 Database Statistics:');
  console.log('=======================');
  
  try {
    // Test total listings
    const allListings = await fetch(`${BASE_URL}/api/listings/search?limit=1000`);
    const allData = await allListings.json();
    console.log(`Total Listings: ${allData.totalCount}`);
    
    // Test by property type
    const propertyTypes = ['apartment', 'house', 'room', 'studio'];
    for (const type of propertyTypes) {
      const typeResponse = await fetch(`${BASE_URL}/api/listings/search?propertyType=${type}&limit=1000`);
      const typeData = await typeResponse.json();
      console.log(`${type.charAt(0).toUpperCase() + type.slice(1)}s: ${typeData.totalCount}`);
    }
    
    // Test featured listings
    const featuredResponse = await fetch(`${BASE_URL}/api/listings/search?sortBy=featured&limit=1000`);
    const featuredData = await featuredResponse.json();
    const featuredCount = featuredData.data.filter(item => item.isFeatured).length;
    console.log(`Featured Listings: ${featuredCount}`);
    
    // Test price ranges
    const priceRanges = [
      { min: 0, max: 15000, label: 'Under PKR 15,000' },
      { min: 15000, max: 25000, label: 'PKR 15,000 - 25,000' },
      { min: 25000, max: 35000, label: 'PKR 25,000 - 35,000' },
      { min: 35000, max: 999999, label: 'Above PKR 35,000' }
    ];
    
    console.log('\nPrice Distribution:');
    for (const range of priceRanges) {
      const priceResponse = await fetch(`${BASE_URL}/api/listings/search?priceMin=${range.min}&priceMax=${range.max}&limit=1000`);
      const priceData = await priceResponse.json();
      console.log(`${range.label}: ${priceData.totalCount}`);
    }
    
  } catch (error) {
    console.log(`❌ Error fetching database stats: ${error.message}`);
  }
}

// Run all tests
async function main() {
  await runTests();
  await testDatabaseStats();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTests, testDatabaseStats };
