/**
 * Database Seeding Script for RoomShare Application
 * Creates comprehensive test data including users, listings, and admin
 * Run with: node scripts/seedDatabase.js
 */

const { MongoClient } = require('mongodb');
const { createAdminUser } = require('./createAdminUser');
const { generateUser, generateListing, CONFIG } = require('./generateComprehensiveData');

// Database connection
const uri = 'mongodb+srv://imrangulhassan:<EMAIL>/?retryWrites=true&w=majority&appName=RoomShare';

async function clearExistingData(db) {
  console.log('🧹 Clearing existing data...');
  
  try {
    // Clear collections but keep admin user
    await db.collection('listings').deleteMany({});
    await db.collection('users').deleteMany({ email: { $ne: '<EMAIL>' } });
    
    console.log('✅ Existing data cleared (admin user preserved)');
  } catch (error) {
    console.error('❌ Error clearing data:', error);
    throw error;
  }
}

async function createUsers(db) {
  console.log('👥 Creating users...');
  
  const users = [];
  const userIds = [];
  
  // Create regular users (70%)
  for (let i = 0; i < Math.floor(CONFIG.USERS_COUNT * 0.7); i++) {
    const user = await generateUser(i, 'user');
    users.push(user);
  }
  
  // Create listers (30%)
  for (let i = Math.floor(CONFIG.USERS_COUNT * 0.7); i < CONFIG.USERS_COUNT; i++) {
    const user = await generateUser(i, 'lister');
    users.push(user);
  }
  
  try {
    const result = await db.collection('users').insertMany(users);
    console.log(`✅ Created ${result.insertedCount} users`);
    
    // Return the inserted user IDs for creating listings
    return Object.values(result.insertedIds);
  } catch (error) {
    console.error('❌ Error creating users:', error);
    throw error;
  }
}

async function createListings(db, userIds) {
  console.log('🏠 Creating listings...');
  
  // Filter to get only lister user IDs
  const listerUsers = await db.collection('users').find({ role: 'lister' }).toArray();
  const listerIds = listerUsers.map(user => user._id);
  
  if (listerIds.length === 0) {
    console.log('⚠️ No lister users found, creating listings with random user IDs');
    // Fallback to using any user IDs
    const allUsers = await db.collection('users').find({}).toArray();
    listerIds.push(...allUsers.map(user => user._id));
  }
  
  const listings = [];
  
  for (let i = 0; i < CONFIG.LISTINGS_COUNT; i++) {
    // Randomly assign listings to listers (some listers can have multiple listings)
    const randomListerIndex = Math.floor(Math.random() * listerIds.length);
    const ownerId = listerIds[randomListerIndex];
    
    const listing = generateListing(i, ownerId);
    listings.push(listing);
  }
  
  try {
    const result = await db.collection('listings').insertMany(listings);
    console.log(`✅ Created ${result.insertedCount} listings`);
    
    return result.insertedCount;
  } catch (error) {
    console.error('❌ Error creating listings:', error);
    throw error;
  }
}

async function createIndexes(db) {
  console.log('📊 Creating database indexes...');
  
  try {
    // Listings indexes
    await db.collection('listings').createIndex({ 'address.location': '2dsphere' });
    await db.collection('listings').createIndex({ 
      title: 'text', 
      description: 'text', 
      'address.city': 'text',
      'address.country': 'text' 
    });
    await db.collection('listings').createIndex({ 'price.amount': 1 });
    await db.collection('listings').createIndex({ propertyType: 1 });
    await db.collection('listings').createIndex({ status: 1 });
    await db.collection('listings').createIndex({ isFeatured: 1 });
    await db.collection('listings').createIndex({ createdAt: -1 });
    await db.collection('listings').createIndex({ 'availability.availableFrom': 1 });
    
    // Users indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ role: 1 });
    await db.collection('users').createIndex({ isVerified: 1 });
    
    console.log('✅ Database indexes created');
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    // Don't throw error for indexes as they might already exist
  }
}

async function generateStatistics(db) {
  console.log('📈 Generating statistics...');
  
  try {
    const userStats = await db.collection('users').aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]).toArray();
    
    const listingStats = await db.collection('listings').aggregate([
      {
        $group: {
          _id: '$propertyType',
          count: { $sum: 1 },
          avgPrice: { $avg: '$price.amount' }
        }
      }
    ]).toArray();
    
    const totalUsers = await db.collection('users').countDocuments();
    const totalListings = await db.collection('listings').countDocuments();
    const featuredListings = await db.collection('listings').countDocuments({ isFeatured: true });
    const verifiedUsers = await db.collection('users').countDocuments({ isVerified: true });
    
    console.log('\n📊 DATABASE STATISTICS:');
    console.log('========================');
    console.log(`Total Users: ${totalUsers}`);
    console.log(`Verified Users: ${verifiedUsers}`);
    console.log(`Total Listings: ${totalListings}`);
    console.log(`Featured Listings: ${featuredListings}`);
    
    console.log('\n👥 Users by Role:');
    userStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count}`);
    });
    
    console.log('\n🏠 Listings by Type:');
    listingStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} (Avg: PKR ${Math.round(stat.avgPrice)})`);
    });
    
  } catch (error) {
    console.error('❌ Error generating statistics:', error);
  }
}

async function seedDatabase() {
  let client;
  
  try {
    console.log('🚀 Starting database seeding...');
    console.log(`📍 Target: ${CONFIG.USERS_COUNT} users, ${CONFIG.LISTINGS_COUNT} listings`);
    
    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    console.log('✅ Connected to MongoDB Atlas');
    
    const db = client.db();
    
    // Step 1: Clear existing data (except admin)
    await clearExistingData(db);
    
    // Step 2: Create admin user
    console.log('👑 Creating admin user...');
    await createAdminUser();
    
    // Step 3: Create users
    const userIds = await createUsers(db);
    
    // Step 4: Create listings
    await createListings(db, userIds);
    
    // Step 5: Create database indexes
    await createIndexes(db);
    
    // Step 6: Generate statistics
    await generateStatistics(db);
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n🔗 You can now:');
    console.log('  • Test search functionality at /search');
    console.log('  • Login as admin: <EMAIL> / admin@12345');
    console.log('  • Browse listings with realistic data');
    console.log('  • Test all search filters and features');
    
  } catch (error) {
    console.error('💥 Database seeding failed:', error);
    process.exit(1);
  } finally {
    if (client) {
      await client.close();
      console.log('🔌 MongoDB connection closed');
    }
  }
}

// Run the seeding script
if (require.main === module) {
  seedDatabase().catch(console.error);
}

module.exports = { seedDatabase };
