#!/bin/bash

# Search Functionality Demo Script
# Showcases the robust search capabilities

BASE_URL="http://localhost:3000"

echo "🎯 RoomShare Search Functionality Demo"
echo "======================================"
echo ""

# Function to display search results nicely
demo_search() {
    local title="$1"
    local endpoint="$2"
    local description="$3"
    
    echo "🔍 $title"
    echo "   $description"
    echo "   URL: $BASE_URL$endpoint"
    
    response=$(curl -s "$BASE_URL$endpoint")
    count=$(echo $response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')
    
    if [ ! -z "$count" ]; then
        echo "   Results: $count listings found"
        
        # Extract first listing title if available
        first_title=$(echo $response | grep -o '"title":"[^"]*"' | head -1 | sed 's/"title":"//g' | sed 's/"//g')
        if [ ! -z "$first_title" ]; then
            echo "   Example: $first_title"
        fi
    else
        echo "   Results: No data returned"
    fi
    echo ""
}

echo "🏠 PROPERTY TYPE SEARCHES"
echo "========================"

demo_search "Apartments Only" \
    "/api/listings/search?propertyType=apartment&limit=5" \
    "Find all apartment listings"

demo_search "Houses for Families" \
    "/api/listings/search?propertyType=house&limit=5" \
    "Find house listings suitable for families"

demo_search "Budget Rooms" \
    "/api/listings/search?propertyType=room&priceMax=15000&limit=5" \
    "Find affordable room rentals under PKR 15,000"

demo_search "Modern Studios" \
    "/api/listings/search?propertyType=studio&amenities=wifi,airConditioning&limit=5" \
    "Find modern studios with WiFi and AC"

echo "💰 PRICE-BASED SEARCHES"
echo "======================="

demo_search "Budget Options" \
    "/api/listings/search?priceMax=20000&limit=5" \
    "Find listings under PKR 20,000"

demo_search "Mid-Range Properties" \
    "/api/listings/search?priceMin=20000&priceMax=35000&limit=5" \
    "Find listings between PKR 20,000 - 35,000"

demo_search "Premium Properties" \
    "/api/listings/search?priceMin=35000&limit=5" \
    "Find premium listings above PKR 35,000"

echo "🏡 AMENITY-BASED SEARCHES"
echo "========================="

demo_search "WiFi Enabled" \
    "/api/listings/search?amenities=wifi&limit=5" \
    "Find listings with WiFi connectivity"

demo_search "Parking Available" \
    "/api/listings/search?amenities=parking&limit=5" \
    "Find listings with parking facilities"

demo_search "Fully Furnished" \
    "/api/listings/search?furnished=true&limit=5" \
    "Find fully furnished properties"

demo_search "Pet-Friendly" \
    "/api/listings/search?amenities=petsAllowed&limit=5" \
    "Find pet-friendly accommodations"

echo "🛏️ ROOM-SPECIFIC SEARCHES"
echo "========================="

demo_search "2-Bedroom Properties" \
    "/api/listings/search?bedrooms=2&limit=5" \
    "Find properties with exactly 2 bedrooms"

demo_search "3+ Bedroom Houses" \
    "/api/listings/search?propertyType=house&bedrooms=3&limit=5" \
    "Find large houses with 3+ bedrooms"

demo_search "Single Bathroom" \
    "/api/listings/search?bathrooms=1&limit=5" \
    "Find properties with 1 bathroom"

echo "🔍 TEXT SEARCHES"
echo "==============="

demo_search "Search: 'Furnished Apartment'" \
    "/api/listings/search?q=furnished%20apartment&limit=5" \
    "Text search for furnished apartments"

demo_search "Search: 'Latifabad'" \
    "/api/listings/search?q=Latifabad&limit=5" \
    "Text search for properties in Latifabad"

demo_search "Search: 'Modern'" \
    "/api/listings/search?q=modern&limit=5" \
    "Text search for modern properties"

echo "⭐ FEATURED & SORTED SEARCHES"
echo "============================"

demo_search "Featured Properties" \
    "/api/listings/search?sortBy=featured&limit=5" \
    "Show featured listings first"

demo_search "Cheapest First" \
    "/api/listings/search?sortBy=price&sortOrder=asc&limit=5" \
    "Sort by price: low to high"

demo_search "Most Expensive" \
    "/api/listings/search?sortBy=price&sortOrder=desc&limit=5" \
    "Sort by price: high to low"

demo_search "Newest Listings" \
    "/api/listings/search?sortBy=createdAt&sortOrder=desc&limit=5" \
    "Sort by newest listings first"

echo "🎯 COMPLEX COMBINED SEARCHES"
echo "============================"

demo_search "Perfect Student Apartment" \
    "/api/listings/search?propertyType=apartment&priceMax=25000&amenities=wifi&students=true&limit=5" \
    "Affordable apartments with WiFi for students"

demo_search "Family House with Parking" \
    "/api/listings/search?propertyType=house&bedrooms=3&amenities=parking&families=true&limit=5" \
    "Large houses with parking for families"

demo_search "Professional Studio" \
    "/api/listings/search?propertyType=studio&amenities=wifi,airConditioning&professionals=true&furnished=true&limit=5" \
    "Furnished studios with amenities for professionals"

demo_search "Budget Room in Hyderabad" \
    "/api/listings/search?propertyType=room&city=Hyderabad&priceMax=15000&amenities=wifi&limit=5" \
    "Affordable rooms in Hyderabad with WiFi"

echo "📊 SEARCH STATISTICS"
echo "==================="

# Get total counts for different categories
total_response=$(curl -s "$BASE_URL/api/listings/search?limit=1000")
total_count=$(echo $total_response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')

echo "📈 Database Overview:"
echo "   Total Listings: $total_count"

# Property type breakdown
for type in apartment house room studio; do
    type_response=$(curl -s "$BASE_URL/api/listings/search?propertyType=$type&limit=1000")
    type_count=$(echo $type_response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')
    echo "   ${type^}s: $type_count"
done

# Featured count
featured_response=$(curl -s "$BASE_URL/api/listings/search?sortBy=featured&limit=1000")
featured_count=$(echo $featured_response | grep -o '"isFeatured":true' | wc -l)
echo "   Featured: $featured_count"

# Price ranges
echo ""
echo "💰 Price Distribution:"
price_ranges=("0-15000:Under 15k" "15000-25000:15k-25k" "25000-35000:25k-35k" "35000-999999:Above 35k")
for range in "${price_ranges[@]}"; do
    IFS=':' read -r price_range label <<< "$range"
    IFS='-' read -r min_price max_price <<< "$price_range"
    range_response=$(curl -s "$BASE_URL/api/listings/search?priceMin=$min_price&priceMax=$max_price&limit=1000")
    range_count=$(echo $range_response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')
    echo "   PKR $label: $range_count listings"
done

echo ""
echo "🎉 Demo Complete!"
echo ""
echo "🔗 Try the search functionality yourself:"
echo "   Homepage: $BASE_URL"
echo "   Search Page: $BASE_URL/search"
echo "   API Docs: See SEARCH_FUNCTIONALITY_FINAL.md"
echo ""
echo "✨ The search system is fully functional with:"
echo "   • 100 realistic listings"
echo "   • 15+ filter options"
echo "   • Text search capabilities"
echo "   • Geographic search"
echo "   • Performance optimizations"
echo "   • Mobile responsive design"
