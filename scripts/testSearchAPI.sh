#!/bin/bash

# Search Functionality Test Suite
# Tests all search features with curl commands

BASE_URL="http://localhost:3000"
PASSED=0
FAILED=0

echo "🧪 Starting Search Functionality Tests..."
echo "========================================"

# Test function
test_api() {
    local test_name="$1"
    local endpoint="$2"
    local expected_pattern="$3"
    
    echo "Testing: $test_name"
    
    response=$(curl -s "$BASE_URL$endpoint")
    
    if [[ $response == *"$expected_pattern"* ]]; then
        echo "✅ PASSED"
        ((PASSED++))
    else
        echo "❌ FAILED - Expected pattern not found: $expected_pattern"
        echo "Response: ${response:0:200}..."
        ((FAILED++))
    fi
    echo ""
}

# Test 1: Basic Search
test_api "Basic Search - All Listings" \
    "/api/listings/search?limit=5" \
    '"success":true'

# Test 2: Property Type Filter
test_api "Property Type Filter - Apartments" \
    "/api/listings/search?propertyType=apartment&limit=3" \
    '"propertyType":"apartment"'

# Test 3: Price Range Filter
test_api "Price Range Filter (15k-30k)" \
    "/api/listings/search?priceMin=15000&priceMax=30000&limit=3" \
    '"amount":'

# Test 4: Text Search
test_api "Text Search - 'apartment'" \
    "/api/listings/search?q=apartment&limit=3" \
    '"data":'

# Test 5: Bedroom Filter
test_api "Bedroom Filter - 2 bedrooms" \
    "/api/listings/search?bedrooms=2&limit=3" \
    '"bedrooms":2'

# Test 6: Amenity Filter - WiFi
test_api "Amenity Filter - WiFi" \
    "/api/listings/search?amenities=wifi&limit=3" \
    '"wifi":true'

# Test 7: City Filter
test_api "City Filter - Hyderabad" \
    "/api/listings/search?city=Hyderabad&limit=3" \
    '"city":"Hyderabad"'

# Test 8: Furnished Filter
test_api "Furnished Filter" \
    "/api/listings/search?furnished=true&limit=3" \
    '"furnished":true'

# Test 9: Featured Sort
test_api "Featured Listings Sort" \
    "/api/listings/search?sortBy=featured&limit=5" \
    '"isFeatured":'

# Test 10: Price Sort
test_api "Price Sort - Ascending" \
    "/api/listings/search?sortBy=price&sortOrder=asc&limit=3" \
    '"amount":'

# Test 11: Combined Filters
test_api "Combined Filters (apartment + price + wifi)" \
    "/api/listings/search?propertyType=apartment&priceMax=25000&amenities=wifi&limit=3" \
    '"propertyType":"apartment"'

# Test 12: Pagination
test_api "Pagination Test" \
    "/api/listings/search?limit=2&skip=5" \
    '"data":'

echo "📊 TEST SUMMARY"
echo "==============="
echo "Total Tests: $((PASSED + FAILED))"
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Success Rate: $(( PASSED * 100 / (PASSED + FAILED) ))%"

if [ $FAILED -eq 0 ]; then
    echo ""
    echo "🎉 All tests passed! Search functionality is working correctly."
else
    echo ""
    echo "⚠️  $FAILED test(s) failed. Please check the implementation."
fi

# Database Statistics
echo ""
echo "📈 Database Statistics:"
echo "======================"

# Total listings
total_response=$(curl -s "$BASE_URL/api/listings/search?limit=1000")
total_count=$(echo $total_response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')
echo "Total Listings: $total_count"

# Property type breakdown
for type in apartment house room studio; do
    type_response=$(curl -s "$BASE_URL/api/listings/search?propertyType=$type&limit=1000")
    type_count=$(echo $type_response | grep -o '"totalCount":[0-9]*' | grep -o '[0-9]*')
    echo "${type^}s: $type_count"
done

# Featured listings
featured_response=$(curl -s "$BASE_URL/api/listings/search?sortBy=featured&limit=1000")
featured_count=$(echo $featured_response | grep -o '"isFeatured":true' | wc -l)
echo "Featured Listings: $featured_count"

echo ""
echo "🔗 Test the search functionality manually at:"
echo "   Homepage: $BASE_URL"
echo "   Search Page: $BASE_URL/search"
echo "   API Endpoint: $BASE_URL/api/listings/search"
