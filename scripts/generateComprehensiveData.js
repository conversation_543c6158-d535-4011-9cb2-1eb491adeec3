/**
 * Comprehensive Data Generator for RoomShare Application
 * Generates realistic users, listings, and other data for testing
 * Run with: node scripts/generateComprehensiveData.js
 */

const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const { createAdminUser } = require('./createAdminUser');

// Database connection
const uri = 'mongodb+srv://imrangulhassan:<EMAIL>/?retryWrites=true&w=majority&appName=RoomShare';

// Configuration
const CONFIG = {
  USERS_COUNT: 50,
  LISTINGS_COUNT: 100,
  CENTER_LAT: 27.5382272,  // Hyderabad, Pakistan
  CENTER_LNG: 68.2033152,
  MAX_DISTANCE_KM: 15,
};

// Data arrays for realistic generation
const FIRST_NAMES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
];

const <PERSON><PERSON>_<PERSON><PERSON><PERSON> = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Mughal', 'Bhatti', 'Gondal', 'Cheema', 'Jatt', 'Arain', 'Gujjar'
];

const PROPERTY_TYPES = ['apartment', 'house', 'room', 'studio'];
const ROOM_TYPES = ['entire_place', 'private_room', 'shared_room'];

const NEIGHBORHOODS = [
  'Latifabad', 'Qasimabad', 'Cantonment', 'City Area', 'Kohsar', 'Gulshan-e-Hadeed',
  'New Hala Naka', 'Thandi Sarak', 'Saddar', 'Defence', 'Gulistan-e-Sarmast', 'Auto Bahn Road'
];

const STREET_NAMES = [
  'Main Road', 'Shahrah-e-Pakistan', 'University Road', 'Jamshoro Road', 'Hala Road',
  'Bypass Road', 'Station Road', 'Market Road', 'School Road', 'Hospital Road'
];

const AMENITIES_LIST = [
  'wifi', 'airConditioning', 'heating', 'tv', 'kitchen', 'washer', 'dryer', 
  'parking', 'elevator', 'security', 'gym', 'pool', 'furnished'
];

const OCCUPATIONS = [
  'Student', 'Software Engineer', 'Teacher', 'Doctor', 'Businessman', 'Government Employee',
  'Bank Employee', 'Shopkeeper', 'Engineer', 'Accountant', 'Lawyer', 'Nurse'
];

// Utility functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomElements(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateRandomCoordinate(centerLat, centerLng, maxDistanceKm) {
  const earthRadius = 6371;
  const maxDistanceRadians = maxDistanceKm / earthRadius;
  const randomDistance = Math.random() * maxDistanceRadians;
  const randomAngle = Math.random() * 2 * Math.PI;
  
  const newLat = Math.asin(
    Math.sin(centerLat * (Math.PI / 180)) * Math.cos(randomDistance) +
    Math.cos(centerLat * (Math.PI / 180)) * Math.sin(randomDistance) * Math.cos(randomAngle)
  ) * (180 / Math.PI);
  
  const newLng = centerLng * (Math.PI / 180) + Math.atan2(
    Math.sin(randomAngle) * Math.sin(randomDistance) * Math.cos(centerLat * (Math.PI / 180)),
    Math.cos(randomDistance) - Math.sin(centerLat * (Math.PI / 180)) * Math.sin(newLat * (Math.PI / 180))
  ) * (180 / Math.PI);
  
  return { lat: newLat, lng: newLng };
}

function generateRandomPrice(propertyType, roomType) {
  let basePrice;
  
  switch (propertyType) {
    case 'room':
      basePrice = roomType === 'shared_room' ? 8000 : 12000;
      break;
    case 'studio':
      basePrice = 15000;
      break;
    case 'apartment':
      basePrice = 25000;
      break;
    case 'house':
      basePrice = 40000;
      break;
    default:
      basePrice = 20000;
  }
  
  // Add random variation (±30%)
  const variation = 0.3;
  const randomFactor = 1 + (Math.random() - 0.5) * 2 * variation;
  return Math.round(basePrice * randomFactor);
}

function generateFutureDate(daysFromNow = 0, maxDaysFromNow = 90) {
  const today = new Date();
  const futureDate = new Date(today);
  const randomDays = Math.floor(Math.random() * (maxDaysFromNow - daysFromNow)) + daysFromNow;
  futureDate.setDate(today.getDate() + randomDays);
  return futureDate;
}

// Generate a realistic user
async function generateUser(index, role = 'user') {
  const firstName = getRandomElement(FIRST_NAMES);
  const lastName = getRandomElement(LAST_NAMES);
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@example.com`;
  
  // Hash password
  const salt = await bcrypt.genSalt(10);
  const hashedPassword = await bcrypt.hash('password123', salt);
  
  return {
    firstName,
    lastName,
    email,
    password: hashedPassword,
    accountType: role === 'lister' ? 'landlord' : 'tenant',
    role,
    authProviders: ['credentials'],
    isVerified: Math.random() > 0.2, // 80% verified
    profileImage: `https://ui-avatars.com/api/?name=${firstName}+${lastName}&background=random`,
    bio: `${getRandomElement(OCCUPATIONS)} living in Hyderabad. Looking for ${role === 'lister' ? 'reliable tenants' : 'a comfortable place to stay'}.`,
    phone: `+92-300-${Math.floor(Math.random() * 9000000) + 1000000}`,
    city: 'Hyderabad',
    country: 'Pakistan',
    occupation: getRandomElement(OCCUPATIONS),
    preferences: {
      smoking: Math.random() > 0.7,
      pets: Math.random() > 0.6,
      gender: getRandomElement(['male', 'female', '']),
      ageRange: getRandomElement(['18-25', '26-35', '36-45', '46+', ''])
    },
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // Random date in last year
    updatedAt: new Date()
  };
}

// Generate a realistic listing
function generateListing(index, ownerId) {
  const propertyType = getRandomElement(PROPERTY_TYPES);
  const roomType = propertyType === 'room' ? getRandomElement(ROOM_TYPES) : 'entire_place';
  const neighborhood = getRandomElement(NEIGHBORHOODS);
  const coordinates = generateRandomCoordinate(CONFIG.CENTER_LAT, CONFIG.CENTER_LNG, CONFIG.MAX_DISTANCE_KM);
  
  const bedrooms = propertyType === 'studio' ? 0 : Math.floor(Math.random() * 4) + 1;
  const bathrooms = Math.floor(Math.random() * 3) + 1;
  
  // Generate amenities
  const amenities = {};
  AMENITIES_LIST.forEach(amenity => {
    amenities[amenity] = Math.random() > 0.5;
  });
  
  // Ensure some basic amenities are more common
  amenities.wifi = Math.random() > 0.1; // 90% have wifi
  amenities.kitchen = propertyType !== 'room' || Math.random() > 0.3; // Most have kitchen except some rooms
  
  const price = generateRandomPrice(propertyType, roomType);
  
  return {
    owner: ownerId,
    title: `${propertyType.charAt(0).toUpperCase() + propertyType.slice(1)} in ${neighborhood}`,
    description: `Beautiful ${bedrooms > 0 ? bedrooms + ' bedroom ' : ''}${propertyType} located in ${neighborhood}. ${
      amenities.furnished ? 'Fully furnished with modern amenities. ' : ''
    }${Math.random() > 0.5 ? 'Recently renovated and well-maintained. ' : ''}${
      Math.random() > 0.6 ? 'Close to public transportation and shopping centers.' : 'Quiet neighborhood with easy access to main roads.'
    }`,
    propertyType,
    roomType,
    address: {
      street: `${Math.floor(Math.random() * 200) + 1} ${getRandomElement(STREET_NAMES)}`,
      city: 'Hyderabad',
      state: 'Sindh',
      zipCode: `${Math.floor(Math.random() * 90000) + 10000}`,
      country: 'Pakistan',
      location: {
        type: 'Point',
        coordinates: [coordinates.lng, coordinates.lat]
      }
    },
    price: {
      amount: price,
      currency: 'PKR',
      paymentPeriod: 'monthly'
    },
    size: {
      value: propertyType === 'room' ? Math.floor(Math.random() * 30) + 15 : Math.floor(Math.random() * 100) + 50,
      unit: 'sqm'
    },
    rooms: {
      bedrooms,
      bathrooms
    },
    amenities,
    images: [
      {
        url: `https://picsum.photos/800/600?random=${index}`,
        caption: `${propertyType} exterior`,
        isMain: true
      },
      {
        url: `https://picsum.photos/800/600?random=${index + 1000}`,
        caption: 'Interior view'
      },
      {
        url: `https://picsum.photos/800/600?random=${index + 2000}`,
        caption: amenities.kitchen ? 'Kitchen' : 'Living area'
      }
    ],
    availability: {
      availableFrom: generateFutureDate(0, 60),
      minimumStay: {
        value: Math.floor(Math.random() * 11) + 1,
        unit: 'months'
      },
      maximumStay: {
        value: Math.floor(Math.random() * 24) + 12,
        unit: 'months'
      }
    },
    rules: {
      noParties: Math.random() > 0.3,
      quiet: Math.random() > 0.4,
      additionalRules: Math.random() > 0.7 ? 'No smoking indoors. Visitors allowed with prior notice.' : ''
    },
    preferredTenants: {
      students: Math.random() > 0.5,
      professionals: Math.random() > 0.4,
      couples: propertyType !== 'room' && Math.random() > 0.6,
      families: propertyType === 'house' && Math.random() > 0.5,
      gender: getRandomElement(['any', 'male', 'female'])
    },
    status: 'published',
    isFeatured: Math.random() > 0.8, // 20% featured
    featuredUntil: Math.random() > 0.8 ? generateFutureDate(1, 30) : null,
    views: Math.floor(Math.random() * 500),
    createdAt: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000), // Random date in last 6 months
    updatedAt: new Date()
  };
}

module.exports = {
  generateUser,
  generateListing,
  CONFIG,
  FIRST_NAMES,
  LAST_NAMES,
  PROPERTY_TYPES,
  NEIGHBORHOODS
};
